const axios = require("axios");
const SubOrder = require("../../models/SubOrder");
const Package = require("../../models/Package");
const Contact = require("../../models/Contact");
const User = require("../../models/User");
const OrderStatus = require("../../constants/enums/order-status");

// API configuration
const API_BASE_URL = process.env.API_BASE_URL_TEST;
const DOMAIN_CHECK_API_BASE_URL =
  process.env.API_BASE_URL_TEST + "/domaincheck";

const AUTH_PARAMS = {
  "auth-userid": process.env.AUTH_USERID_TEST,
  "api-key": process.env.API_KEY_TEST,
};

// Company customer ID for domain registrations (from environment)
const COMPANY_CUSTOMER_ID = process.env.COMPANY_CUSTOMER_ID || "31174676";

/**
 * Get default nameservers from API
 * @returns {Promise<Array>} Array of nameservers
 */
async function getDefaultNameservers() {
  try {
    console.log("🔍 Fetching customer default nameservers from API...");
    const response = await axios.get(
      `${API_BASE_URL}/domains/customer-default-ns.json`,
      {
        params: {
          ...AUTH_PARAMS,
          "customer-id": COMPANY_CUSTOMER_ID,
        },
        timeout: 10000,
      }
    );

    console.log("✅ API nameserver response:", response.data);

    if (Array.isArray(response.data) && response.data.length > 0) {
      console.log("✅ Using dynamic nameservers from API:", response.data);
      return response.data;
    } else {
      throw new Error("API returned empty nameservers");
    }
  } catch (error) {
    console.error("❌ Error fetching nameservers from API:", error.message);

    // Fallback to hardcoded nameservers only if API fails
    const fallbackNameservers = [
      "moha1280036.earth.orderbox-dns.com",
      "moha1280036.mars.orderbox-dns.com",
      "moha1280036.mercury.orderbox-dns.com",
      "moha1280036.venus.orderbox-dns.com",
    ];

    console.log("⚠️ Using fallback nameservers:", fallbackNameservers);
    return fallbackNameservers;
  }
}

/**
 * Get user's domain contacts for registration
 * @param {String} userId - The user ID
 * @returns {Promise<Object>} - Object containing contact IDs
 */
async function getUserDomainContacts(userId) {
  try {
    console.log("🔄 Fetching user domain contacts for user:", userId);

    const user = await User.findById(userId).populate(
      "domainContacts.registrant domainContacts.admin domainContacts.tech domainContacts.billing"
    );

    if (!user) {
      throw new Error("User not found");
    }

    const contacts = {
      registrant: user.domainContacts.registrant?.externalContactId,
      admin: user.domainContacts.admin?.externalContactId,
      tech: user.domainContacts.tech?.externalContactId,
      billing: user.domainContacts.billing?.externalContactId,
    };

    console.log("📋 Retrieved user contacts:", contacts);

    // Check for missing contacts
    const missingContacts = [];
    Object.entries(contacts).forEach(([type, contactId]) => {
      if (!contactId) {
        missingContacts.push(type);
      }
    });

    if (missingContacts.length > 0) {
      console.warn("⚠️ Missing contact types:", missingContacts);
      throw new Error(
        `Missing required contacts: ${missingContacts.join(
          ", "
        )}. Please ensure all contact types are configured for the user.`
      );
    }

    return contacts;
  } catch (error) {
    console.error("❌ Error fetching user domain contacts:", error);
    throw error;
  }
}

/**
 * Process domain registration for an order
 * @param {Object} order - The order object with populated subOrders
 * @returns {Promise<Array>} - Array of registration results
 */
exports.processDomainRegistrations = async (order) => {
  try {
    console.log("🔄 Processing domain registrations for order:", order._id);

    if (!order.subOrders || order.subOrders.length === 0) {
      console.log("ℹ️ No suborders found in order");
      return [];
    }

    // Find domain suborders by checking if the package name contains a domain pattern
    const domainSubOrders = [];

    for (const subOrder of order.subOrders) {
      // Skip if package is not populated
      if (!subOrder.package) {
        console.log("⚠️ SubOrder has no package:", subOrder._id);
        continue;
      }

      // Check if this is a domain package by checking the reference
      if (
        subOrder.package.reference &&
        subOrder.package.reference.startsWith("domain-")
      ) {
        console.log("✅ Found domain suborder:", subOrder._id);
        domainSubOrders.push(subOrder);
      }
    }

    if (domainSubOrders.length === 0) {
      console.log("ℹ️ No domain suborders found in order");
      return [];
    }

    // Process each domain suborder
    const registrationResults = await Promise.all(
      domainSubOrders.map(async (subOrder) => {
        return await registerDomain(subOrder, order);
      })
    );

    return registrationResults;
  } catch (error) {
    console.error("❌ Error processing domain registrations:", error);
    throw error;
  }
};

/**
 * Check domain availability before registration
 * @param {string} domainName - Domain name to check
 * @returns {Promise<boolean>} - True if available, false otherwise
 */
async function checkDomainAvailability(domainName) {
  try {
    console.log("🔍 Checking availability for domain:", domainName);

    // Split domain name and TLD
    const parts = domainName.split(".");
    const domain = parts[0];
    const tld = parts.slice(1).join(".");

    const response = await axios.get(`${API_BASE_URL}/domaincheck`, {
      params: {
        ...AUTH_PARAMS,
        "domain-name": [domain],
        tlds: [tld],
      },
      timeout: 10000,
    });

    console.log("✅ Domain availability response:", response.data);

    // Check if domain is available
    const domainData = response.data[domainName];
    const isAvailable = domainData && domainData.status === "available";

    console.log(
      `📋 Domain ${domainName} availability:`,
      isAvailable ? "AVAILABLE" : "NOT AVAILABLE"
    );
    return isAvailable;
  } catch (error) {
    console.error("❌ Error checking domain availability:", error.message);
    throw new Error(`Failed to check domain availability: ${error.message}`);
  }
}

/**
 * Register a domain for a specific suborder
 * @param {Object} subOrder - The suborder containing domain information
 * @param {Object} order - The parent order
 * @returns {Promise<Object>} - Registration result
 */
async function registerDomain(subOrder, order) {
  try {
    // Extract domain name from package name
    const domainName = subOrder.package.name;
    console.log("🔄 Starting domain registration process for:", domainName);

    // Step 1: Check domain availability
    const isAvailable = await checkDomainAvailability(domainName);
    if (!isAvailable) {
      throw new Error(`Domain ${domainName} is not available for registration`);
    }

    // Step 2: Get dynamic nameservers from API
    const nameservers = await getDefaultNameservers();

    // Step 3: Get user's domain contacts
    const userContacts = await getUserDomainContacts(order.user._id);

    // Step 4: Prepare registration parameters
    const params = {
      ...AUTH_PARAMS,
      "domain-name": domainName,
      years: subOrder.period || 1,
      "customer-id": COMPANY_CUSTOMER_ID,
      "reg-contact-id": userContacts.registrant,
      "admin-contact-id": userContacts.admin,
      "tech-contact-id": userContacts.tech,
      "billing-contact-id": userContacts.billing,
      "invoice-option": "NoInvoice", // Use NoInvoice to deduct from funds
      "auto-renew": false, // Default to false for auto-renewal
      "purchase-privacy": subOrder.privacyProtection || false, // Use user's choice
      "protect-privacy": subOrder.privacyProtection || false,
    };

    console.log("🔧 Domain registration parameters:", {
      domain: domainName,
      customerId: COMPANY_CUSTOMER_ID,
      contacts: userContacts,
      nameservers: nameservers,
      privacyProtection: subOrder.privacyProtection || false,
    });

    // Step 5: Create URLSearchParams for proper nameserver handling
    const searchParams = new URLSearchParams();

    // Add all parameters except nameservers
    for (const [key, value] of Object.entries(params)) {
      searchParams.append(key, value);
    }

    // Add each nameserver as a separate 'ns' parameter
    nameservers.forEach((nameserver) => {
      searchParams.append("ns", nameserver);
    });

    console.log("🌐 Final API URL parameters:", searchParams.toString());

    // Step 6: Call the domain registration API
    const response = await axios.post(
      `${API_BASE_URL}/domains/register.json?${searchParams.toString()}`,
      null,
      {
        timeout: 30000, // Increased timeout for domain registration
      }
    );

    console.log("✅ Domain registration successful:", response.data);

    // Extract order ID from the response
    const orderId = response.data?.orderId || response.data?.order_id;

    // Update suborder with order ID and status
    await SubOrder.findByIdAndUpdate(subOrder._id, {
      status: OrderStatus.ACTIVE,
      orderId: orderId, // Store the order ID
    });

    return {
      subOrderId: subOrder._id,
      domainName,
      success: true,
      registrationData: response.data,
    };
  } catch (error) {
    console.error(
      "❌ Domain registration error:",
      error.response?.data || error.message
    );

    return {
      subOrderId: subOrder._id,
      domainName: subOrder.package.name,
      success: false,
      error: error.response?.data || error.message,
    };
  }
}
