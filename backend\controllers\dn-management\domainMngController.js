const axios = require("axios");
const TldPricing = require("../../models/TldPricing");
const DomainPricing = require("../../models/DomainPricing");
const { tldProductKeyMap } = require("../../utils/tldMapping");
const User = require("../../models/User");

const API_BASE_URL = process.env.API_BASE_URL_TEST;
const DOMAIN_CHECK_API_BASE_URL =
  process.env.API_BASE_URL_TEST + "/domaincheck";

const AUTH_PARAMS = {
  "auth-userid": process.env.AUTH_USERID_TEST,
  "api-key": process.env.API_KEY_TEST,
};

// Company account for domain registrations (if using the company account approach)
const COMPANY_ACCOUNT = {
  customerId: process.env.COMPANY_CUSTOMER_ID || "",
  regContactId: process.env.COMPANY_REG_CONTACT_ID || "",
  adminContactId: process.env.COMPANY_ADMIN_CONTACT_ID || "",
  techContactId: process.env.COMPANY_TECH_CONTACT_ID || "",
  billingContactId: process.env.COMPANY_BILLING_CONTACT_ID || "",
};

// Log the company account details for debugging
console.log("Company account configuration:", {
  customerId: COMPANY_ACCOUNT.customerId,
  regContactId: COMPANY_ACCOUNT.regContactId,
  adminContactId: COMPANY_ACCOUNT.adminContactId,
  techContactId: COMPANY_ACCOUNT.techContactId,
  billingContactId: COMPANY_ACCOUNT.billingContactId,
});

// Check Domain Availability
exports.checkDomainAvailability = async (req, res) => {
  const { domain, tld } = req.query;
  console.log("req.query: ", req.query);
  try {
    const response = await axios.get(
      `${DOMAIN_CHECK_API_BASE_URL}/domains/available.json`,
      {
        params: { ...AUTH_PARAMS, "domain-name": domain, tlds: tld },
      }
    );
    console.log("check domain availability: ", response.data);
    res.json(response.data);
  } catch (error) {
    console.log("error: ", error);
    res.status(500).json({ error: error.message });
  }
};

// Check IDN Domain Availability
exports.checkIdnDomainAvailability = async (req, res) => {
  const { domains, tld, idnLanguageCode } = req.query;
  console.log("checkidnavailability: ", req.query);

  try {
    const params = {
      ...AUTH_PARAMS,
      tld, // Changed from tlds to tld
      idnLanguageCode,
    };

    // Handle multiple domain names
    if (Array.isArray(domains)) {
      // Add each domain as a separate domain-name parameter
      domains.forEach((domain) => {
        params["domain-name"] = domain;
      });
    } else {
      params["domain-name"] = domains;
    }

    const response = await axios.get(
      `${DOMAIN_CHECK_API_BASE_URL}/domains/idn-available.json`,
      { params }
    );

    // Process the response
    const result = response.data;
    console.log("check idn domain availability: ", result);
    const formattedResponse = {
      domain: Object.keys(result)[0],
      available: result[Object.keys(result)[0]].status !== "regthroughothers",
      status: result[Object.keys(result)[0]].status,
      classKey: result[Object.keys(result)[0]].classkey,
    };
    res.json(formattedResponse);
  } catch (error) {
    console.log("error: ", error);
    res.status(500).json({ error: error.message });
  }
};

// Check Premium Domain Availability
exports.checkPremiumDomainAvailability = async (req, res) => {
  console.log("req.query: ", req.query);
  const { keyword, tlds, priceHigh, priceLow, numberOfResults } = req.query;

  try {
    const response = await axios.get(
      `${DOMAIN_CHECK_API_BASE_URL}/domains/premium/available.json`,
      {
        params: {
          ...AUTH_PARAMS,
          "key-word": keyword,
          tlds: Array.isArray(tlds) ? tlds.join(",") : tlds,
          "price-high": priceHigh,
          "price-low": priceLow,
          "no-of-results": numberOfResults || 10,
        },
      }
    );
    res.json(response.data);
  } catch (error) {
    console.log("error: ", error);
    res.status(500).json({ error: error.message });
  }
};

// Suggest Domain Names
exports.suggestDomainNames = async (req, res) => {
  const {
    keyword,
    tldOnly, // This will be a string if provided
    exactMatch = "true", // Query params are strings, handle boolean conversion
    adult = "false", // Query params are strings, handle boolean conversion
  } = req.query; // Changed from req.query.params

  console.log("suggestDomainNames req.query:", req.query);

  try {
    // Input validation
    if (!keyword) {
      return res.status(400).json({
        error: "Keyword is required",
      });
    }

    // Validate keyword format (a-z, A-Z, 0-9, space and hyphen)
    const keywordRegex = /^[a-zA-Z0-9\s-]+$/;
    if (!keywordRegex.test(keyword)) {
      return res.status(400).json({
        error: "Keyword can only contain letters, numbers, spaces, and hyphens",
      });
    }

    const params = {
      ...AUTH_PARAMS,
      keyword,
      "exact-match": exactMatch === "true", // Convert string to boolean
      adult: adult === "true", // Convert string to boolean
    };

    // Add tld-only parameter if provided
    if (tldOnly) {
      params["tld-only"] = tldOnly;
    }

    // console.log("Sending suggestion request with params:", params);

    const response = await axios.get(
      `${API_BASE_URL}/domains/v5/suggest-names.json`,
      { params }
    );

    // console.log("Suggestion API raw response:", response.data);

    // Return the raw suggestions data
    res.json(response.data);
  } catch (error) {
    console.error(
      "Suggestion API error:",
      error.response?.data || error.message
    );
    res.status(500).json({ error: error.message });
  }
};

exports.getDNPricing = async (req, res) => {
  const { customerId } = req.query;
  console.log("customerId: ", customerId);
  try {
    const params = {
      ...AUTH_PARAMS,
    };

    if (customerId) {
      params["customer-id"] = customerId;
    }

    const response = await axios.get(
      `${API_BASE_URL}/products/customer-price.json`,
      { params }
    );

    console.log("getDNPricing: ", response.data);
    res.json(response.data);
  } catch (error) {
    console.log("error: ", error);
    res.status(500).json({ error: error.message });
  }
};

exports.handleDomainSearch = async (req, res) => {
  try {
    const searchInput = req.query.params;
    console.log("Extracted searchInput from req.query.params: ", searchInput);

    if (
      !searchInput ||
      typeof searchInput !== "string" ||
      searchInput.trim() === ""
    ) {
      return res
        .status(400)
        .json({ error: "Please enter a domain name to search." });
    }

    const trimmedInput = searchInput.trim().toLowerCase();
    const parts = trimmedInput.split(".");
    let keyword;
    let tldStr;

    if (
      parts.length === 1 ||
      (parts[0] === "" && parts.length === 2 && parts[1] !== "")
    ) {
      keyword = parts[parts.length - 1];
      tldStr = "com";
    } else if (parts.length >= 2 && parts[0] !== "") {
      tldStr = parts.pop();
      keyword = parts.join(".");
    } else {
      return res.status(400).json({ error: "Invalid domain format." });
    }

    if (!keyword) {
      return res.status(400).json({ error: "Invalid domain keyword." });
    }

    // Query TldPricing for the main TLD
    const mainTldPricing = await TldPricing.findOne({ tld: tldStr });

    // Check domain availability via API
    const availabilityResponse = await axios.get(
      `${DOMAIN_CHECK_API_BASE_URL}/domains/available.json`,
      {
        params: { ...AUTH_PARAMS, "domain-name": keyword, tlds: tldStr },
        timeout: 20000,
      }
    );
    const availabilityData = availabilityResponse?.data || {};
    console.log("availabilityData: ", availabilityData);
    const primaryDomainFullName = `${keyword}.${tldStr}`;
    let primaryResult = null;

    // Use a Map to collect unique premium results from primary check and suggestions
    const premiumResultsMap = new Map();

    // --- PREMIUM DOMAIN CHECK FOR PRIMARY DOMAIN ---
    let primaryPremiumPrice = null;
    let primaryPremiumCurrency = "MAD"; // Assuming MAD based on the API response format
    try {
      // Split the domain into SLD and TLD for the premium check
      const [sld, ...tldParts] = primaryDomainFullName.split(".");
      const tld = tldParts.join(".");
      const premiumCheckResponse = await axios.get(
        `${DOMAIN_CHECK_API_BASE_URL}/domains/premium/available.json`,
        {
          params: {
            ...AUTH_PARAMS,
            "key-word": sld,
            tlds: tld,
            "no-of-results": 1,
          },
          timeout: 10000,
        }
      );
      console.log(
        "premiumCheckResponse (primary): ",
        premiumCheckResponse.data
      );
      if (
        premiumCheckResponse.data &&
        premiumCheckResponse.data[primaryDomainFullName]
      ) {
        // Correctly extract price from the response structure
        primaryPremiumPrice = parseFloat(
          premiumCheckResponse.data[primaryDomainFullName]
        );
        // Add primary premium result to the map
        premiumResultsMap.set(primaryDomainFullName, {
          name: primaryDomainFullName,
          price: primaryPremiumPrice, // Use the extracted price
          currency: primaryPremiumCurrency, // Use the assumed currency
          type: "premium",
          // Set isAvailable based on original availability status
          isAvailable:
            availabilityData[primaryDomainFullName]?.status === "available" ||
            availabilityData[primaryDomainFullName]?.status ===
              "regthroughothers",
        });
      }
    } catch (premiumErr) {
      // If premium check fails, just log and continue
      console.error(
        "Premium check error (primary):",
        premiumErr?.response?.data || premiumErr.message
      );
    }

    const domainKey = Object.keys(availabilityData)[0];
    if (domainKey && availabilityData[domainKey]) {
      const originalStatus = availabilityData[domainKey].status; // Keep original status
      const status =
        originalStatus === "available" ? "available" : "unavailable";
      const isRegThroughOthers = originalStatus === "regthroughothers";
      const isUnknown = originalStatus === "unknown";

      // Determine if primary domain is premium based on the premium check result
      const isPrimaryPremium = !!primaryPremiumPrice; // Check if a premium price was found

      primaryResult = {
        name: domainKey,
        status: isUnknown ? "unknown" : status,
        reason: isUnknown
          ? "Registry connection unavailable, please retry later"
          : originalStatus === "available" // If original status is available, no reason needed
          ? null
          : isRegThroughOthers && !isPrimaryPremium // If regthroughothers AND not premium
          ? "Unavailable" // Set reason to "Unavailable"
          : originalStatus, // Otherwise, use the original status as the reason (e.g., "taken")
        canTransfer: isRegThroughOthers,
        // Use pricing from TldPricing for non-premium available domains
        pricing:
          !isPrimaryPremium && status === "available" && mainTldPricing
            ? {
                register: mainTldPricing.raw.addnewdomain,
                renewal: mainTldPricing.raw.renewdomain,
                transfer: mainTldPricing.raw.addtransferdomain,
                period: mainTldPricing.period,
                currency: mainTldPricing.currency,
              }
            : null,
        isPremium: isPrimaryPremium,
        // Use pricing from premium check for premium domains
        premiumPricing: isPrimaryPremium
          ? {
              register: primaryPremiumPrice, // Use the extracted premium price
              currency: primaryPremiumCurrency, // Use the extracted premium currency
            }
          : null,
      };

      // If primary domain is premium, update the map entry with correct availability status
      if (isPrimaryPremium) {
        premiumResultsMap.set(primaryResult.name, {
          name: primaryResult.name,
          price: primaryResult.premiumPricing?.register,
          currency: primaryResult.premiumPricing?.currency,
          type: "premium",
          // Set isAvailable based on original availability status
          isAvailable:
            availabilityData[primaryResult.name]?.status === "available" ||
            availabilityData[primaryResult.name]?.status === "regthroughothers",
        });
      }
    } else {
      // Handle case where primary availability check fails
      const isPrimaryPremium = !!primaryPremiumPrice;
      primaryResult = {
        name: primaryDomainFullName,
        status: "error",
        reason: "Failed to check availability",
        pricing: null,
        isPremium: isPrimaryPremium,
        premiumPricing: isPrimaryPremium
          ? {
              register: primaryPremiumPrice,
              currency: primaryPremiumCurrency,
            }
          : null,
      };
      // If primary domain is premium despite availability check error, add to map
      if (isPrimaryPremium) {
        premiumResultsMap.set(primaryResult.name, {
          name: primaryResult.name,
          price: primaryResult.premiumPricing?.register,
          currency: primaryResult.premiumPricing?.currency,
          type: "premium",
          isAvailable: false, // Mark as not available if primary check failed
        });
      }
    }

    // Fetch suggestions in parallel (do not wait for fallback)
    const suggestionParams = {
      ...AUTH_PARAMS,
      keyword,
      "exact-match": false,
      adult: false,
    };

    let suggestionResults = [];
    try {
      const suggestionsApiResponse = await axios.get(
        `${API_BASE_URL}/domains/v5/suggest-names.json`,
        {
          params: suggestionParams,
          timeout: 20000,
        }
      );
      const suggestionsData = suggestionsApiResponse.data;
      // console.log("suggestionsData44: ", suggestionsData);
      if (suggestionsData && typeof suggestionsData === "object") {
        // Get all TLDs from suggestions
        const tldsToFetch = [
          ...new Set(
            Object.keys(suggestionsData)
              .map((name) => {
                const parts = name.split(".");
                return parts.length > 1 ? parts[parts.length - 1] : null;
              })
              .filter(Boolean)
          ),
        ];
        // Query all relevant TldPricing docs at once
        const tldPricings = await TldPricing.find({
          tld: { $in: tldsToFetch },
        });
        const tldPricingMap = {};
        tldPricings.forEach((doc) => {
          tldPricingMap[doc.tld] = doc;
        });

        const suggestionPromises = Object.entries(suggestionsData)
          .filter(
            ([name, details]) =>
              name.toLowerCase() !== primaryDomainFullName.toLowerCase() &&
              (details.status === "available" ||
                details.status === "regthroughothers")
          )
          .slice(0, 20) // Limit suggestions to 10
          .map(async ([name, details]) => {
            const tld = name.split(".").pop();
            const pricingDoc = tldPricingMap[tld];
            return {
              name: name,
              status: details.status,
              pricing: pricingDoc
                ? {
                    register: pricingDoc.raw.addnewdomain,
                    renewal: pricingDoc.raw.renewdomain,
                    transfer: pricingDoc.raw.addtransferdomain,
                    period: pricingDoc.period,
                    currency: pricingDoc.currency,
                  }
                : null,
            };
          });
        suggestionResults = await Promise.all(suggestionPromises);
      }
    } catch (suggestError) {
      // Ignore suggestion errors, fallback below
      console.error(
        "Suggestion API error:",
        suggestError.response?.data || suggestError.message
      );
    }

    // --- Construct Final Response ---
    const finalResponse = {
      available: [],
      unavailable: [],
      premium: [],
      suggestions: suggestionResults || [], // Suggestions that are not premium
    };

    // Handle primary result
    if (primaryResult) {
      if (primaryResult.isPremium) {
        // Primary premium domain is already added to premiumResultsMap
        // No need to add here, it will be added from the map later
      } else if (
        primaryResult.status === "available" &&
        primaryResult.pricing
      ) {
        finalResponse.available.push({
          name: primaryResult.name,
          pricing: primaryResult.pricing,
        });
      } else if (
        primaryResult.status === "unavailable" ||
        primaryResult.status === "error" ||
        (primaryResult.status === "available" && !primaryResult.pricing)
      ) {
        finalResponse.unavailable.push({
          name: primaryResult.name,
          reason:
            primaryResult.reason ||
            (primaryResult.status === "available"
              ? "Pricing not available"
              : "Domain unavailable or error during check"),
        });
      }
    }

    // Populate the premium array from the map
    finalResponse.premium = Array.from(premiumResultsMap.values());
    console.log("finalResponse: ", finalResponse.available[0]);
    console.log(
      `[TIMING] handleDomainSearch finished at ${new Date().toISOString()}`
    );
    res.json(finalResponse);
  } catch (error) {
    console.error("Unhandled error in handleDomainSearch:", error);
    res.status(500).json({ error: "An internal server error occurred." });
  }
};

// Add domain to cart
exports.addDomainToCart = async (req, res) => {
  const {
    domain,
    tld,
    price,
    period = 1,
    privacyProtection = false,
    autoRenew = false,
  } = req.body;
  console.log("Request body:", req.body);

  try {
    // Validate input
    if (!domain || !tld) {
      return res.status(400).json({
        success: false,
        message: "Domain name and TLD are required",
      });
    }

    // Check if domain is available before adding to cart
    const availabilityResponse = await axios.get(
      `${DOMAIN_CHECK_API_BASE_URL}/domains/available.json`,
      {
        params: { ...AUTH_PARAMS, "domain-name": domain, tlds: tld },
      }
    );

    const domainKey = `${domain}.${tld}`;
    const isAvailable =
      availabilityResponse.data[domainKey]?.status === "available";

    if (!isAvailable) {
      return res.status(400).json({
        success: false,
        message: "Domain is not available for registration",
      });
    }

    // If price is not provided, get it from the API
    let domainPrice = parseFloat(price) || 0;
    if (!domainPrice) {
      return res.status(400).json({
        success: false,
        message: "Price is required",
      });
    }

    // Create a cart item for the domain
    const Cart = require("../../models/Cart");

    // Use req.user._id if available, otherwise create a temporary ID for guest users
    const userId = req.user?._id;
    if (!userId) {
      return res.status(401).json({ message: "User not authenticated" });
    }

    let cart = await Cart.findOne({ user: userId });

    if (!cart) {
      cart = new Cart({
        user: userId,
        isGuest: !req.user?._id,
        items: [],
      });
    }

    // Get raw pricing data for the domain's TLD to support dynamic period selection
    const TldPricing = require("../../models/TldPricing");
    const tldPricingDoc = await TldPricing.findOne({ tld: tld });
    const rawPricing = tldPricingDoc ? tldPricingDoc.raw : null;

    console.log("TLD pricing lookup for:", tld);
    console.log("TLD pricing doc found:", !!tldPricingDoc);
    console.log(
      "Raw pricing data:",
      rawPricing ? "Available" : "Not available"
    );

    // Add domain to cart using the new method
    await cart.addDomain(
      domainKey,
      tld,
      domainPrice,
      period,
      rawPricing,
      privacyProtection,
      autoRenew
    );

    res.status(200).json({
      success: true,
      message: "Domain added to cart",
      cart,
    });
  } catch (error) {
    console.error("Error adding domain to cart:", error);
    res.status(500).json({
      success: false,
      error: error.message,
    });
  }
};

// Get Reseller Pricing
exports.getResellerPricing = async (req, res) => {
  // const { resellerId } = req.query.params;
  // console.log("getResellerPricing: ", req.query.params);
  try {
    const params = {
      ...AUTH_PARAMS,
    };

    params["reseller-id"] = AUTH_PARAMS["auth-userid"];
    // if (resellerId) {
    // }

    const response = await axios.get(
      `${API_BASE_URL}/products/reseller-price.json`,
      { params }
    );

    res.json(response.data);
  } catch (error) {
    console.log("error: ", error);
    res.status(500).json({ error: error.message });
  }
};

// POST /domainMng/sync-tld-pricing
exports.syncTldPricing = async (req, res) => {
  try {
    // 1. Fetch pricing from external API
    const response = await axios.get(
      `${API_BASE_URL}/products/customer-price.json`,
      { params: { ...AUTH_PARAMS } }
    );
    const pricingData = response.data;
    if (!pricingData || typeof pricingData !== "object") {
      console.error(
        "syncTldPricing: Invalid or empty pricingData",
        pricingData
      );
      return res
        .status(500)
        .json({ error: "Failed to fetch pricing data from API" });
    }
    console.log("pricingddd : ", Object.keys(pricingData).length);
    // 2. Map and store pricing for each supported TLD
    const results = [];
    for (const [tld, tldKey] of Object.entries(tldProductKeyMap)) {
      const tldPricingRaw = pricingData[tldKey];
      if (!tldPricingRaw) continue;

      const register = Number(tldPricingRaw["addnewdomain"]?.["1"] || 0);
      const renewal = Number(tldPricingRaw["renewdomain"]?.["1"] || 0);
      const transfer = Number(tldPricingRaw["addtransferdomain"]?.["1"] || 0);
      const currency = "MAD"; // or extract if available

      const doc = await TldPricing.findOneAndUpdate(
        { tld },
        {
          tld,
          tldKey,
          register,
          renewal,
          transfer,
          period: 1,
          currency,
          raw: tldPricingRaw,
        },
        { upsert: true, new: true }
      );
      results.push(doc);
    }

    return res.status(200).json({
      message: "TLD pricing synced successfully",
      count: results.length,
      data: results,
    });
  } catch (error) {
    console.error("syncTldPricing error:", error);
    return res.status(500).json({ error: error.message });
  }
};

// Sync Domain Pricing from external API
exports.syncDomainPricing = async (req, res) => {
  try {
    console.log("Fetching domain pricing from external API...");
    const response = await axios.get(
      `${API_BASE_URL}/products/customer-price.json`,
      { params: AUTH_PARAMS } // Assuming no customerId is needed for the general price list
    );

    const pricingData = response.data;
    // console.log("Received pricing data:", pricingData);

    // Find and update the existing document or create a new one
    // We'll assume there's only one document for global pricing
    const filter = {}; // Empty filter to find any document
    const update = pricingData; // The fetched data is the update
    const options = {
      upsert: true, // Create the document if it doesn't exist
      new: true, // Return the updated document
      setDefaultsOnInsert: true, // Apply schema defaults if creating
    };

    const updatedPricing = await DomainPricing.findOneAndUpdate(
      filter,
      update,
      options
    );
    return res.status(200).json({
      message: "Domain pricing synced successfully",
      count: updatedPricing.length,
      data: updatedPricing,
    });
    // console.log("Domain pricing synced successfully:", updatedPricing);
  } catch (error) {
    console.error("Error syncing domain pricing:", error.message);
    res.status(500).json({
      message: "Failed to sync domain pricing",
      error: error.message,
    });
  }
};
// Get Domain Pricing from database
exports.getDomainPricing = async (req, res) => {
  try {
    // Retrieve the single pricing document from the database
    const pricing = await DomainPricing.findOne({}); // Find the first document

    if (!pricing) {
      return res.status(404).json({
        message: "Domain pricing data not found. Please sync it first.",
      });
    }

    // Extract the top-level keys from the pricing data object
    // The actual pricing data is stored in the document itself,
    // excluding Mongoose internal properties like _id, __v, timestamps.
    // We can get the keys from the plain JavaScript object representation.
    const pricingObject = pricing.toObject();
    const pricingKeys = Object.keys(pricingObject).filter(
      (key) =>
        key !== "_id" &&
        key !== "__v" &&
        key !== "createdAt" &&
        key !== "updatedAt"
    );

    res.status(200).json({
      message: "Domain pricing keys retrieved successfully",
      data: pricingKeys, // Return only the keys
      count: pricingKeys.length,
    });
  } catch (error) {
    console.error("Error retrieving domain pricing keys:", error.message);
    res.status(500).json({
      message: "Failed to retrieve domain pricing keys",
      error: error.message,
    });
  }
};

// Customer Signup in Reseller System
exports.customerSignup = async (req, res) => {
  const {
    username,
    passwd,
    name,
    company,
    addressLine1,
    city,
    state,
    country,
    zipcode,
    phoneCc,
    phone,
    langPref = "en",
    skipEmailVerification = false, // New parameter to control email verification
    userId, // Optional: ID of the user in your main system
  } = req.body;

  try {
    // Check if we should use silent signup (no email verification)
    // The reseller API doesn't have a direct way to disable email verification,
    // so we'll need to handle this differently

    const response = await axios.post(
      `${API_BASE_URL}/customers/v2/signup.json`,
      null,
      {
        params: {
          ...AUTH_PARAMS,
          username,
          passwd,
          name,
          company,
          "address-line-1": addressLine1,
          city,
          state,
          country,
          zipcode,
          "phone-cc": phoneCc,
          phone,
          "lang-pref": langPref,
        },
      }
    );

    // If userId is provided, store the reseller customer ID with the user
    if (userId && response.data && response.data.entityid) {
      try {
        // Update the user record with the reseller customer ID
        await User.findByIdAndUpdate(userId, {
          $set: { resellerCustomerId: response.data.entityid },
        });
      } catch (userUpdateError) {
        console.error(
          "Error updating user with reseller customer ID:",
          userUpdateError
        );
        // Continue anyway, as the customer was created successfully
      }
    }

    res.json(response.data);
  } catch (error) {
    console.error(
      "Customer signup error:",
      error.response?.data || error.message
    );
    res.status(500).json({
      error: error.response?.data?.message || error.message,
      details: error.response?.data,
    });
  }
};

// Get Domain Details by Name (from registration system)
exports.getDomainDetailsByName = async (req, res) => {
  const logger = require("../../utils/globalLogger")("getDomainDetailsByName");
  try {
    const { domainName, options = "All" } = req.query;

    if (!domainName) {
      return res.status(400).json({
        success: false,
        error: "Domain name is required",
      });
    }

    logger.log(
      `🔍 Getting domain details for: ${domainName} with options: ${options}`
    );

    const apiUrl = `${API_BASE_URL}/domains/details-by-name.json`;
    const apiParams = {
      ...AUTH_PARAMS,
      "domain-name": domainName,
      options: options,
    };

    logger.log("🌐 API URL:", apiUrl);
    logger.log("🔑 API Params:", apiParams);

    // Call the reseller API to get domain details
    const response = await axios.get(apiUrl, {
      params: apiParams,
      timeout: 15000,
    });

    logger.log("✅ Raw domain details response:", response.data);

    // Transform the API response to a more frontend-friendly format
    const domainDetails = response.data;
    const transformedData = {
      // Basic domain information
      domainName: domainDetails.domainname || domainName,
      orderId: domainDetails.orderid,
      status: domainDetails.currentstatus || "unknown",
      orderStatus: domainDetails.orderstatus,
      domainStatus: domainDetails.domainstatus,

      // Dates
      registrationDate: domainDetails.creationtime,
      expiryDate: domainDetails.endtime,

      // Product information
      productCategory: domainDetails.productcategory,
      productKey: domainDetails.productkey,

      // Customer information
      customerId: domainDetails.customerid,
      isImmediateReseller: domainDetails.isImmediateReseller,

      // Nameservers
      nameservers: [],
      numberOfNameservers: domainDetails.noOfNameServers || 0,

      // Privacy protection
      privacyProtection: {
        allowed: domainDetails.privacyprotectedallowed === "true",
        enabled: domainDetails.isprivacyprotected === "true",
        expiryDate: domainDetails.privacyprotectendtime,
        registrantProtected:
          domainDetails["privacy-registrantcontact"] === "true",
        adminProtected: domainDetails["privacy-admincontact"] === "true",
        techProtected: domainDetails["privacy-techcontact"] === "true",
        billingProtected: domainDetails["privacy-billingcontact"] === "true",
      },

      // Auto renewal
      autoRenew: domainDetails.recurring === "true",

      // Contact IDs
      contacts: {
        registrantId: domainDetails.registrantcontactid,
        adminId: domainDetails.admincontactid,
        techId: domainDetails.techcontactid,
        billingId: domainDetails.billingcontactid,
      },

      // Contact details (if included in response)
      contactDetails: {},

      // GDPR
      gdpr: domainDetails.gdpr,

      // Domain locks and security
      locks: {
        orderSuspended: domainDetails.isOrderSuspendedUponExpiry === "true",
        parentSuspended: domainDetails.orderSuspendedByParent === "true",
      },

      // RAA verification
      raaVerification: {
        status: domainDetails.raaVerificationStatus,
        startTime: domainDetails.raaVerificationStartTime,
      },

      // DNSSEC
      dnssec: domainDetails.dnssec,

      // Raw API response for debugging
      rawApiResponse: domainDetails,
    };

    // Extract nameservers
    for (let i = 1; i <= 13; i++) {
      const ns = domainDetails[`ns${i}`];
      if (ns) {
        transformedData.nameservers.push(ns);
      }
    }

    // Extract contact details if available
    if (domainDetails.registrantcontact) {
      transformedData.contactDetails.registrant =
        domainDetails.registrantcontact;
    }
    if (domainDetails.admincontact) {
      transformedData.contactDetails.admin = domainDetails.admincontact;
    }
    if (domainDetails.techcontact) {
      transformedData.contactDetails.tech = domainDetails.techcontact;
    }
    if (domainDetails.billingcontact) {
      transformedData.contactDetails.billing = domainDetails.billingcontact;
    }

    logger.log("✅ Transformed domain details:", transformedData);

    res.json({
      success: true,
      domain: transformedData,
      message: "Domain details retrieved successfully",
    });
  } catch (error) {
    logger.error(
      "❌ Error getting domain details:",
      error.response?.data || error.message
    );
    res.status(500).json({
      success: false,
      error: "Failed to retrieve domain details",
      details: error.response?.data || error.message,
    });
  }
};

// Get Domain Details (legacy - for availability checking)
exports.getDomainDetails = async (req, res) => {
  try {
    const { domainName } = req.query;

    if (!domainName) {
      return res.status(400).json({
        success: false,
        error: "Domain name is required",
      });
    }

    // Split domain name to get domain and TLD
    const domainParts = domainName.split(".");
    if (domainParts.length < 2) {
      return res.status(400).json({
        success: false,
        error: "Invalid domain name format",
      });
    }

    const domain = domainParts[0];
    const tld = domainParts.slice(1).join(".");

    console.log(
      `🔍 Getting domain availability for: ${domainName} (domain: ${domain}, tld: ${tld})`
    );

    // Call the Heberjahiz API to check domain availability
    const response = await axios.get(
      `${DOMAIN_CHECK_API_BASE_URL}/domains/available.json`,
      {
        params: {
          ...AUTH_PARAMS,
          "domain-name": domain,
          tlds: tld,
        },
        timeout: 15000,
      }
    );

    console.log("✅ Domain availability response:", response.data);

    // Format the response for frontend consumption
    const domainDetails = response.data;

    res.json({
      success: true,
      domain: domainDetails,
      message: "Domain availability retrieved successfully",
    });
  } catch (error) {
    console.error(
      "❌ Error getting domain availability:",
      error.response?.data || error.message
    );
    res.status(500).json({
      success: false,
      error: "Failed to retrieve domain availability",
      details: error.response?.data || error.message,
    });
  }
};

// Renew Domain
exports.renewDomain = async (req, res) => {
  try {
    const { domainName, years = 1, orderId } = req.body;

    if (!domainName && !orderId) {
      return res.status(400).json({
        success: false,
        error: "Either domain name or order ID is required",
      });
    }

    if (years < 1 || years > 10) {
      return res.status(400).json({
        success: false,
        error: "Years must be between 1 and 10",
      });
    }

    console.log(
      `🔄 Renewing domain: ${
        domainName || `Order ID: ${orderId}`
      } for ${years} year(s)`
    );

    // Prepare parameters for renewal
    const params = {
      ...AUTH_PARAMS,
      years,
      "invoice-option": "NoInvoice",
    };

    // Add either domain name or order ID
    if (orderId) {
      params["order-id"] = orderId;
    } else {
      // Split domain name to get domain and TLD
      const domainParts = domainName.split(".");
      if (domainParts.length < 2) {
        return res.status(400).json({
          success: false,
          error: "Invalid domain name format",
        });
      }

      const domain = domainParts[0];
      const tld = domainParts.slice(1).join(".");

      params["domain-name"] = domain;
      params["tlds"] = tld;
    }

    // Call the Heberjahiz API to renew domain
    const response = await axios.post(
      `${API_BASE_URL}/domains/renew.json`,
      null,
      {
        params,
        timeout: 15000,
      }
    );

    console.log("✅ Domain renewal response:", response.data);

    res.json({
      success: true,
      renewal: response.data,
      message: "Domain renewal initiated successfully",
    });
  } catch (error) {
    console.error(
      "❌ Error renewing domain:",
      error.response?.data || error.message
    );
    res.status(500).json({
      success: false,
      error: "Failed to renew domain",
      details: error.response?.data || error.message,
    });
  }
};

// Get Domain Order ID
exports.getDomainOrderId = async (req, res) => {
  try {
    console.log("🔍 Full request query:", req.query);
    const { domainName } = req.query;

    if (!domainName) {
      console.log("❌ Domain name is missing from query:", req.query);
      return res.status(400).json({
        success: false,
        error: "Domain name is required",
      });
    }

    console.log(`🔍 Getting order ID for domain: ${domainName}`);

    const apiUrl = `${API_BASE_URL}/domains/orderid.json`;
    const apiParams = {
      ...AUTH_PARAMS,
      "domain-name": domainName,
    };

    console.log("🌐 API URL:", apiUrl);
    console.log("🔑 API Params:", apiParams);

    // Call the Heberjahiz API to get domain order ID
    const response = await axios.get(apiUrl, {
      params: apiParams,
      timeout: 15000,
    });

    console.log("✅ Domain order ID response:", response.data);

    // The API returns the order ID directly as an integer
    const orderId = response.data;

    res.json({
      success: true,
      orderId: orderId,
      domainName: domainName,
      message: "Domain order ID retrieved successfully",
    });
  } catch (error) {
    console.error(
      "❌ Error getting domain order ID:",
      error.response?.data || error.message
    );
    res.status(500).json({
      success: false,
      error: "Failed to retrieve domain order ID",
      details: error.response?.data || error.message,
    });
  }
};

// Get Customer Default Nameservers
exports.getCustomerDefaultNameservers = async (req, res) => {
  try {
    const { customerId } = req.query;

    // Use company customer ID if not provided
    const customerIdToUse =
      customerId || process.env.COMPANY_CUSTOMER_ID || "31174676";

    console.log(
      `🔍 Getting default nameservers for customer: ${customerIdToUse}`
    );

    const apiUrl = `${API_BASE_URL}/domains/customer-default-ns.json`;
    const apiParams = {
      ...AUTH_PARAMS,
      "customer-id": customerIdToUse,
    };

    console.log("🌐 API URL:", apiUrl);
    console.log("🔑 API Params:", apiParams);

    // Call the Heberjahiz API to get customer default nameservers
    const response = await axios.get(apiUrl, {
      params: apiParams,
      timeout: 15000,
    });

    console.log("✅ Raw API response:", response.data);
    console.log("✅ Response type:", typeof response.data);
    console.log("✅ Is array:", Array.isArray(response.data));

    // The API returns an array of nameservers directly
    const nameservers = Array.isArray(response.data) ? response.data : [];

    res.json({
      success: true,
      nameservers: nameservers,
      customerId: customerIdToUse,
      message: "Customer default nameservers retrieved successfully",
    });
  } catch (error) {
    console.error(
      "❌ Error getting customer default nameservers:",
      error.response?.data || error.message
    );
    res.status(500).json({
      success: false,
      error: "Failed to retrieve customer default nameservers",
      details: error.response?.data || error.message,
    });
  }
};

// Modify Nameservers
exports.modifyNameServers = async (req, res) => {
  try {
    const { orderId, nameservers } = req.body;

    if (!orderId) {
      return res.status(400).json({
        success: false,
        error: "Order ID is required for nameserver modification",
      });
    }

    if (!nameservers || !Array.isArray(nameservers) || nameservers.length < 2) {
      return res.status(400).json({
        success: false,
        error: "At least 2 nameservers are required",
      });
    }

    if (nameservers.length > 13) {
      return res.status(400).json({
        success: false,
        error: "Maximum 13 nameservers allowed",
      });
    }

    console.log(`🔧 Modifying nameservers for Order ID: ${orderId}`);
    console.log("New nameservers:", nameservers);

    // Prepare parameters for nameserver modification
    const params = {
      ...AUTH_PARAMS,
      "order-id": orderId,
    };

    // Create URLSearchParams for proper nameserver handling
    const searchParams = new URLSearchParams();

    // Add all parameters except nameservers
    for (const [key, value] of Object.entries(params)) {
      searchParams.append(key, value);
    }

    // Add each nameserver as a separate 'ns' parameter (as per API documentation)
    nameservers.forEach((nameserver) => {
      searchParams.append("ns", nameserver.trim());
    });

    console.log(
      "🌐 Nameserver modification parameters:",
      searchParams.toString()
    );

    // Call the Heberjahiz API to modify nameservers
    const response = await axios.post(
      `${API_BASE_URL}/domains/modify-ns.json?${searchParams.toString()}`,
      null,
      {
        timeout: 15000,
      }
    );

    console.log("✅ Nameserver modification response:", response.data);

    res.json({
      success: true,
      nameservers: response.data,
      message: "Nameservers updated successfully",
    });
  } catch (error) {
    console.error(
      "❌ Error modifying nameservers:",
      error.response?.data || error.message
    );
    res.status(500).json({
      success: false,
      error: "Failed to modify nameservers",
      details: error.response?.data || error.message,
    });
  }
};

// Enable Privacy Protection
exports.enablePrivacyProtection = async (req, res) => {
  try {
    const { domainName, orderId } = req.body;

    if (!domainName && !orderId) {
      return res.status(400).json({
        success: false,
        error: "Either domain name or order ID is required",
      });
    }

    console.log(
      `🔒 Enabling privacy protection for: ${
        domainName || `Order ID: ${orderId}`
      }`
    );

    // Prepare parameters for privacy protection
    const params = {
      ...AUTH_PARAMS,
      "invoice-option": "NoInvoice",
    };

    // Add either domain name or order ID
    if (orderId) {
      params["order-id"] = orderId;
    } else {
      // Split domain name to get domain and TLD
      const domainParts = domainName.split(".");
      if (domainParts.length < 2) {
        return res.status(400).json({
          success: false,
          error: "Invalid domain name format",
        });
      }

      const domain = domainParts[0];
      const tld = domainParts.slice(1).join(".");

      params["domain-name"] = domain;
      params["tlds"] = tld;
    }

    // Call the Heberjahiz API to enable privacy protection
    const response = await axios.post(
      `${API_BASE_URL}/domains/enable-privacy.json`,
      null,
      {
        params,
        timeout: 15000,
      }
    );

    console.log("✅ Privacy protection enabled:", response.data);

    res.json({
      success: true,
      privacy: response.data,
      message: "Privacy protection enabled successfully",
    });
  } catch (error) {
    console.error(
      "❌ Error enabling privacy protection:",
      error.response?.data || error.message
    );
    res.status(500).json({
      success: false,
      error: "Failed to enable privacy protection",
      details: error.response?.data || error.message,
    });
  }
};

// Disable Privacy Protection
exports.disablePrivacyProtection = async (req, res) => {
  try {
    const { domainName, orderId } = req.body;

    if (!domainName && !orderId) {
      return res.status(400).json({
        success: false,
        error: "Either domain name or order ID is required",
      });
    }

    console.log(
      `🔓 Disabling privacy protection for: ${
        domainName || `Order ID: ${orderId}`
      }`
    );

    // Prepare parameters for privacy protection
    const params = {
      ...AUTH_PARAMS,
      "invoice-option": "NoInvoice",
    };

    // Add either domain name or order ID
    if (orderId) {
      params["order-id"] = orderId;
    } else {
      // Split domain name to get domain and TLD
      const domainParts = domainName.split(".");
      if (domainParts.length < 2) {
        return res.status(400).json({
          success: false,
          error: "Invalid domain name format",
        });
      }

      const domain = domainParts[0];
      const tld = domainParts.slice(1).join(".");

      params["domain-name"] = domain;
      params["tlds"] = tld;
    }

    // Call the Heberjahiz API to disable privacy protection
    const response = await axios.post(
      `${API_BASE_URL}/domains/disable-privacy.json`,
      null,
      {
        params,
        timeout: 15000,
      }
    );

    console.log("✅ Privacy protection disabled:", response.data);

    res.json({
      success: true,
      privacy: response.data,
      message: "Privacy protection disabled successfully",
    });
  } catch (error) {
    console.error(
      "❌ Error disabling privacy protection:",
      error.response?.data || error.message
    );
    res.status(500).json({
      success: false,
      error: "Failed to disable privacy protection",
      details: error.response?.data || error.message,
    });
  }
};

// Register Domain
exports.registerDomain = async (req, res) => {
  const {
    domain, // domain name to register
    years = 1, // number of years
    customerId, // customer ID (optional if using company account)
    ns, // nameservers
    regContactId, // registrant contact ID (optional if using company account)
    adminContactId, // admin contact ID (optional if using company account)
    techContactId, // technical contact ID (optional if using company account)
    billingContactId, // billing contact ID (optional if using company account)
    autoRenew = false, // enable/disable auto renewal
    privacyProtection = false, // enable/disable privacy protection
    useCompanyAccount = true, // whether to use the company account
  } = req.body;
  // console.log("req.body: ", req.body);
  try {
    // Determine which customer and contact IDs to use
    const customerIdToUse = useCompanyAccount
      ? COMPANY_ACCOUNT.customerId
      : customerId;

    const regContactIdToUse = useCompanyAccount
      ? COMPANY_ACCOUNT.regContactId
      : regContactId;

    const adminContactIdToUse = useCompanyAccount
      ? COMPANY_ACCOUNT.adminContactId
      : adminContactId;

    const techContactIdToUse = useCompanyAccount
      ? COMPANY_ACCOUNT.techContactId
      : techContactId;

    const billingContactIdToUse = useCompanyAccount
      ? COMPANY_ACCOUNT.billingContactId
      : billingContactId;

    // Validate required parameters
    if (!domain) {
      return res.status(400).json({ error: "Domain name is required" });
    }

    if (!years || years < 1) {
      return res
        .status(400)
        .json({ error: "Valid registration period is required" });
    }

    if (!customerIdToUse) {
      return res.status(400).json({
        error:
          "Customer ID is required. Either provide a customer ID or configure the company account.",
      });
    }

    // Validate contact IDs with more detailed error messages
    const missingContactIds = [];

    if (!regContactIdToUse) {
      missingContactIds.push("Registration Contact ID");
    }

    if (!adminContactIdToUse) {
      missingContactIds.push("Admin Contact ID");
    }

    if (!techContactIdToUse) {
      missingContactIds.push("Technical Contact ID");
    }

    if (!billingContactIdToUse) {
      missingContactIds.push("Billing Contact ID");
    }

    if (missingContactIds.length > 0) {
      return res.status(400).json({
        error: `The following contact IDs are missing or invalid: ${missingContactIds.join(
          ", "
        )}.
                Please check your environment variables or provide valid contact IDs.`,
        missingContactIds,
      });
    }

    // Log the contact IDs being used for debugging
    console.log("Using the following contact IDs for domain registration:", {
      customerIdToUse,
      regContactIdToUse,
      adminContactIdToUse,
      techContactIdToUse,
      billingContactIdToUse,
    });

    // Base parameters required for all domain registrations
    const params = {
      ...AUTH_PARAMS,
      "domain-name": domain,
      years,
      "customer-id": customerIdToUse,
      "reg-contact-id": regContactIdToUse,
      "admin-contact-id": adminContactIdToUse,
      "tech-contact-id": techContactIdToUse,
      "billing-contact-id": billingContactIdToUse,
      "invoice-option": "NoInvoice",
      "auto-renew": autoRenew,
    };

    // The API expects multiple ns parameters with the same name
    if (ns && Array.isArray(ns)) {
      delete params.ns; // Remove the ns array from params
    }

    // Add privacy protection if requested
    if (privacyProtection) {
      params["purchase-privacy"] = true;
      params["protect-privacy"] = true;
    }

    console.log("Domain registration params:", params);

    // Create a URLSearchParams object for the request
    const searchParams = new URLSearchParams();

    // Add all parameters from the params object
    for (const [key, value] of Object.entries(params)) {
      searchParams.append(key, value);
    }

    // Add each nameserver as a separate 'ns' parameter
    if (ns && Array.isArray(ns)) {
      ns.forEach((nameserver) => {
        searchParams.append("ns", nameserver);
      });
    }

    console.log("Final URL parameters:", searchParams.toString());

    // Make the API request with the custom URL parameters
    const response = await axios.post(
      `${API_BASE_URL}/domains/register.json?${searchParams.toString()}`,
      null
    );

    res.json(response.data);
  } catch (error) {
    console.error(
      "Domain registration error:",
      error.response?.data || error.message
    );

    // Check for specific error messages related to contact IDs
    const errorMessage = error.response?.data?.message || error.message;
    let detailedError = errorMessage;

    if (errorMessage && typeof errorMessage === "string") {
      // Check for contact ID related errors
      if (errorMessage.includes("ContactId is not registered by you")) {
        detailedError = `The contact IDs you provided are not registered with your reseller account.
          Please verify that you have created these contacts in your reseller panel and that they are
          associated with your account. You may need to create contacts first using the contacts API.

          Error details: ${errorMessage}`;
      }
    }

    res.status(500).json({
      error: detailedError,
      details: error.response?.data,
      suggestion:
        "Please check that all contact IDs are valid and registered with your reseller account.",
    });
  }
};

// Purchase Privacy Protection
exports.purchasePrivacyProtection = async (req, res) => {
  const logger = require("../../utils/globalLogger")(
    "purchasePrivacyProtection"
  );
  try {
    const {
      orderId,
      invoiceOption = "NoInvoice",
      discountAmount = 0,
    } = req.body;

    if (!orderId) {
      return res.status(400).json({
        success: false,
        error: "Order ID is required",
      });
    }

    logger.log(
      `🛒 [PRIVACY] Purchasing privacy protection for order: ${orderId}`
    );

    const apiUrl = `${API_BASE_URL}/domains/purchase-privacy.json`;
    const apiParams = {
      ...AUTH_PARAMS,
      "order-id": orderId,
      "invoice-option": invoiceOption,
      "discount-amount": discountAmount,
    };

    logger.log("🛒 [PRIVACY] API parameters:", apiParams);

    const response = await axios.post(apiUrl, null, {
      params: apiParams,
      timeout: 15000,
    });

    logger.log(
      "🛒 [PRIVACY] ✅ Privacy protection purchased successfully:",
      response.data
    );

    res.status(200).json({
      success: true,
      message: "Privacy protection purchased successfully",
      data: response.data,
    });
  } catch (error) {
    logger.error(
      "🛒 [PRIVACY] ❌ Error purchasing privacy protection:",
      error.response?.data || error.message
    );
    res.status(500).json({
      success: false,
      error:
        error.response?.data?.message ||
        "Failed to purchase privacy protection",
      details: error.response?.data,
    });
  }
};

// Modify Privacy Protection Status
exports.modifyPrivacyProtection = async (req, res) => {
  const logger = require("../../utils/globalLogger")("modifyPrivacyProtection");
  try {
    const {
      orderId,
      protectPrivacy,
      reason = "User requested privacy protection change",
    } = req.body;

    if (!orderId || typeof protectPrivacy !== "boolean") {
      return res.status(400).json({
        success: false,
        error: "Order ID and protect-privacy (boolean) are required",
      });
    }

    logger.log(
      `🔧 [PRIVACY] Modifying privacy protection for order ${orderId}:`,
      {
        protectPrivacy,
        reason,
      }
    );

    const apiUrl = `${API_BASE_URL}/domains/modify-privacy-protection.json`;
    const apiParams = {
      ...AUTH_PARAMS,
      "order-id": orderId,
      "protect-privacy": protectPrivacy,
      reason: reason,
    };

    logger.log("🔧 [PRIVACY] API parameters:", apiParams);

    const response = await axios.post(apiUrl, null, {
      params: apiParams,
      timeout: 15000,
    });

    logger.log(
      `🔧 [PRIVACY] ✅ Privacy protection ${
        protectPrivacy ? "enabled" : "disabled"
      } successfully:`,
      response.data
    );

    res.status(200).json({
      success: true,
      message: `Privacy protection ${
        protectPrivacy ? "enabled" : "disabled"
      } successfully`,
      data: response.data,
    });
  } catch (error) {
    logger.error(
      "🔧 [PRIVACY] ❌ Error modifying privacy protection:",
      error.response?.data || error.message
    );
    res.status(500).json({
      success: false,
      error:
        error.response?.data?.message || "Failed to modify privacy protection",
      details: error.response?.data,
    });
  }
};

// ===== DNS RECORD MANAGEMENT =====

// Helper function to get domain name from domain ID
const getDomainNameFromId = async (domainId, userId) => {
  try {
    const SubOrder = require('../../models/SubOrder');
    const Order = require('../../models/Order');
    const Package = require('../../models/Package');

    console.log(`🔍 Looking up domain name for ID: ${domainId}, User: ${userId}`);

    // First, try to find by SubOrder ID (if domainId is a SubOrder ID)
    let subOrder = await SubOrder.findById(domainId).populate('package');

    if (subOrder) {
      // Check if this suborder belongs to the user
      const order = await Order.findOne({
        subOrders: subOrder._id,
        user: userId
      });

      if (order && subOrder.package) {
        // Extract domain name from package name or identifiant
        const packageName = subOrder.package.name || subOrder.package.title;
        const domainName = extractDomainFromPackageName(packageName, subOrder.identifiant);

        if (domainName) {
          console.log(`✅ Found domain name: ${domainName}`);
          return domainName;
        }
      }
    }

    // Alternative: Search by domainOrderId field
    subOrder = await SubOrder.findOne({
      domainOrderId: domainId
    }).populate('package');

    if (subOrder) {
      const order = await Order.findOne({
        subOrders: subOrder._id,
        user: userId
      });

      if (order && subOrder.package) {
        const packageName = subOrder.package.name || subOrder.package.title;
        const domainName = extractDomainFromPackageName(packageName, subOrder.identifiant);

        if (domainName) {
          console.log(`✅ Found domain name via domainOrderId: ${domainName}`);
          return domainName;
        }
      }
    }

    console.log(`❌ Domain not found for ID: ${domainId}, User: ${userId}`);
    return null;

  } catch (error) {
    console.error('❌ Error in getDomainNameFromId:', error);
    return null;
  }
};

// Helper function to extract domain name from package name or identifiant
const extractDomainFromPackageName = (packageName, identifiant) => {
  // Try to extract domain from package name (e.g., "Domain Registration - example.com")
  if (packageName) {
    const domainMatch = packageName.match(/([a-zA-Z0-9-]+\.[a-zA-Z]{2,})/);
    if (domainMatch) {
      return domainMatch[1];
    }
  }

  // Try to extract domain from identifiant (e.g., "DOM-example.com-123456")
  if (identifiant) {
    const domainMatch = identifiant.match(/([a-zA-Z0-9-]+\.[a-zA-Z]{2,})/);
    if (domainMatch) {
      return domainMatch[1];
    }
  }

  return null;
};

// Helper function to get domain order ID from domain ID (SubOrder ID)
const getDomainOrderIdFromId = async (domainId, userId) => {
  try {
    const SubOrder = require('../../models/SubOrder');
    const Order = require('../../models/Order');

    console.log(`🔍 Looking up domain order ID for domain ID: ${domainId}, User: ${userId}`);

    // First, try to find by SubOrder ID
    let subOrder = await SubOrder.findById(domainId);

    if (subOrder) {
      // Check if this suborder belongs to the user
      const order = await Order.findOne({
        subOrders: subOrder._id,
        user: userId
      });

      if (order && subOrder.domainOrderId) {
        console.log(`✅ Found domain order ID: ${subOrder.domainOrderId}`);
        return subOrder.domainOrderId;
      }
    }

    // Alternative: Search by domainOrderId field (if domainId is actually the domain order ID)
    subOrder = await SubOrder.findOne({
      domainOrderId: domainId
    });

    if (subOrder) {
      const order = await Order.findOne({
        subOrders: subOrder._id,
        user: userId
      });

      if (order) {
        console.log(`✅ Found domain order ID via lookup: ${domainId}`);
        return domainId; // The domainId was actually the domain order ID
      }
    }

    console.log(`❌ Domain order ID not found for domain ID: ${domainId}, User: ${userId}`);
    return null;

  } catch (error) {
    console.error('❌ Error in getDomainOrderIdFromId:', error);
    return null;
  }
};

// Activate DNS Service for a domain
exports.activateDnsService = async (req, res) => {
  const logger = require("../../utils/globalLogger")("activateDnsService");

  try {
    const { orderId } = req.body;
    const userId = req.user?._id;

    if (!orderId) {
      return res.status(400).json({
        success: false,
        message: "Order ID is required to activate DNS service",
        error: "Missing required parameter: orderId"
      });
    }

    if (!userId) {
      return res.status(401).json({
        success: false,
        message: "User authentication required",
        error: "Missing user ID"
      });
    }

    logger.log(`🔄 [DNS] Activating DNS service for order ID: ${orderId}, User: ${userId}`);

    // Try to get the domain order ID from the provided ID
    // The orderId might be a SubOrder ID, so we need to get the actual domain order ID
    let domainOrderId = await getDomainOrderIdFromId(orderId, userId);

    if (!domainOrderId) {
      // If we can't find it in our database, assume the provided orderId is the domain order ID
      domainOrderId = orderId;
      logger.log(`🔄 [DNS] Using provided order ID as domain order ID: ${domainOrderId}`);
    }

    const apiUrl = `${API_BASE_URL}/dns/activate.xml`;
    const apiParams = {
      "auth-userid": AUTH_PARAMS["auth-userid"],
      "api-key": AUTH_PARAMS["api-key"],
      "order-id": domainOrderId,
    };

    logger.log("🌐 [DNS] API URL:", apiUrl);
    logger.log("🔑 [DNS] API Params:", apiParams);

    // Call the Heberjahiz API to activate DNS service
    const response = await axios.post(apiUrl, null, {
      params: apiParams,
      timeout: 15000,
    });

    logger.log("✅ [DNS] Raw API Response Status:", response.status);
    logger.log("✅ [DNS] Raw API Response Headers:", response.headers);
    logger.log("✅ [DNS] Raw API Response Data:", JSON.stringify(response.data, null, 2));
    logger.log("✅ [DNS] Response Data Type:", typeof response.data);

    // Check if the response indicates success
    // The API might return XML or JSON, handle both cases
    const responseData = response.data;

    // More comprehensive success checking based on the API documentation
    let isSuccess = false;
    let successMessage = "";
    let zoneId = null;
    let actualOrderId = null;

    if (response.status === 200) {
      // Check for XML response patterns (hashtable format)
      if (typeof responseData === "string") {
        logger.log("✅ [DNS] Checking XML hashtable response for success indicators");

        // Check for status = Success in the hashtable
        const hasSuccessStatus = responseData.includes("<string>status</string>") &&
                                responseData.includes("<string>Success</string>");

        // Extract zone ID if present
        const zoneIdMatch = responseData.match(/<string>zoneid<\/string>\s*<string>(\d+)<\/string>/);
        if (zoneIdMatch) {
          zoneId = zoneIdMatch[1];
        }

        // Extract actual order ID if present
        const orderIdMatch = responseData.match(/<string>orderid<\/string>\s*<string>(\d+)<\/string>/);
        if (orderIdMatch) {
          actualOrderId = orderIdMatch[1];
        }

        // Check for "already added zone" message (this means DNS is already active)
        const alreadyActiveMessage = responseData.includes("You have already added zone");

        if (hasSuccessStatus) {
          isSuccess = true;
          if (alreadyActiveMessage) {
            successMessage = "DNS service is already activated for this domain";
          } else {
            successMessage = "DNS service activated successfully";
          }
        }

        logger.log(`✅ [DNS] XML parsing results - Status: ${hasSuccessStatus}, ZoneID: ${zoneId}, OrderID: ${actualOrderId}, AlreadyActive: ${alreadyActiveMessage}`);
      }
      // Check for JSON response patterns
      else if (typeof responseData === "object") {
        logger.log("✅ [DNS] Checking JSON response for success indicators");
        isSuccess = responseData.status === "Success" ||
                   responseData.status === "success" ||
                   responseData.result === "success";
        successMessage = "DNS service activated (JSON response)";
        zoneId = responseData.zoneid;
        actualOrderId = responseData.orderid;
      }
      // Fallback: if we get a 200 status, consider it successful
      else {
        logger.log("✅ [DNS] Using HTTP 200 status as success indicator");
        isSuccess = true;
        successMessage = "DNS service activated (HTTP 200)";
      }
    }

    logger.log(`✅ [DNS] Success determination: ${isSuccess}, Message: ${successMessage}, ZoneID: ${zoneId}, OrderID: ${actualOrderId}`);

    if (isSuccess) {
      logger.log(`✅ [DNS] ${successMessage}`);
      return res.status(200).json({
        success: true,
        message: successMessage,
        data: responseData,
        orderId: domainOrderId,
        actualOrderId: actualOrderId,
        zoneId: zoneId,
        rawResponse: responseData,
        activated: true // Add this flag for frontend
      });
    } else {
      logger.error("❌ [DNS] DNS activation failed - API returned error:", responseData);
      return res.status(500).json({
        success: false,
        message: "Failed to activate DNS service",
        error: responseData.message || responseData.error || "Unknown API error",
        details: responseData,
        activated: false
      });
    }
  } catch (error) {
    logger.error("❌ [DNS] Error activating DNS service:", error.response?.data || error.message);

    // Handle specific API errors
    if (error.response) {
      const apiError = error.response.data;
      return res.status(error.response.status || 500).json({
        success: false,
        message: "DNS service activation failed",
        error: apiError.message || apiError.error || "API request failed",
        details: apiError,
        statusCode: error.response.status
      });
    }

    // Handle network/timeout errors
    return res.status(500).json({
      success: false,
      message: "An error occurred while activating DNS service",
      error: error.message,
      type: error.code || "UNKNOWN_ERROR"
    });
  }
};

// Get DNS Records for a domain (Search DNS Records)
exports.getDnsRecords = async (req, res) => {
  const logger = require("../../utils/globalLogger")("getDnsRecords");
  try {
    const { domainId } = req.params;
    const userId = req.user?.id;

    if (!domainId) {
      return res.status(400).json({
        success: false,
        error: "Domain ID is required",
      });
    }

    logger.log(`🔍 Getting DNS records for domain ID: ${domainId}`);

    // Get the domain name from the domain ID
    const domainName = await getDomainNameFromId(domainId, userId);

    if (!domainName) {
      return res.status(404).json({
        success: false,
        error: "Domain not found or access denied",
      });
    }

    const apiUrl = `${API_BASE_URL}/dns/manage/search-records.json`;
    const apiParams = {
      ...AUTH_PARAMS,
      "domain-name": domainName,
    };

    logger.log("🌐 API URL:", apiUrl);
    logger.log("🔑 API Params:", apiParams);

    // Call the Heberjahiz API to search DNS records
    const response = await axios.get(apiUrl, {
      params: apiParams,
      timeout: 15000,
    });

    logger.log("✅ DNS records response:", response.data);

    // Transform the API response to match our frontend expectations
    const records = response.data || [];

    res.json({
      success: true,
      records: records,
      message: "DNS records retrieved successfully",
    });

  } catch (error) {
    logger.error("❌ Error getting DNS records:", error.response?.data || error.message);
    res.status(500).json({
      success: false,
      error: "Failed to retrieve DNS records",
      details: error.response?.data || error.message,
    });
  }
};

// Add DNS Record
exports.addDnsRecord = async (req, res) => {
  const logger = require("../../utils/globalLogger")("addDnsRecord");
  try {
    const { domainId } = req.params;
    const { record } = req.body;
    const userId = req.user?._id;

    logger.log(`➕ [DNS] Starting DNS record addition for domain ID: ${domainId}, User: ${userId}`);
    logger.log(`➕ [DNS] Record data:`, record);

    if (!domainId || !record) {
      logger.error(`❌ [DNS] Missing required parameters - domainId: ${domainId}, record: ${!!record}`);
      return res.status(400).json({
        success: false,
        error: "Domain ID and record data are required",
      });
    }

    if (!userId) {
      logger.error(`❌ [DNS] User authentication required`);
      return res.status(401).json({
        success: false,
        error: "User authentication required",
      });
    }

    // Validate required record fields
    const { type, name, content, ttl } = record;
    if (!type || !name || !content || !ttl) {
      logger.error(`❌ [DNS] Missing required record fields - type: ${type}, name: ${name}, content: ${content}, ttl: ${ttl}`);
      return res.status(400).json({
        success: false,
        error: "Record type, name, content, and TTL are required",
      });
    }

    logger.log(`➕ [DNS] Adding DNS record for domain ID: ${domainId}`, {
      type,
      name,
      content,
      ttl,
    });

    // Get the domain name from the domain ID
    logger.log(`🔍 [DNS] Looking up domain name for ID: ${domainId}`);
    const domainName = await getDomainNameFromId(domainId, userId);

    if (!domainName) {
      logger.error(`❌ [DNS] Domain not found for ID: ${domainId}, User: ${userId}`);
      return res.status(404).json({
        success: false,
        error: "Domain not found or access denied",
      });
    }

    logger.log(`✅ [DNS] Found domain name: ${domainName}`);

    // Debug environment variables
    logger.log(`🔧 [DNS] API_BASE_URL: ${API_BASE_URL}`);
    logger.log(`🔧 [DNS] AUTH_PARAMS:`, AUTH_PARAMS);

    let apiUrl;
    let apiParams = {
      ...AUTH_PARAMS,
      "domain-name": domainName,
      value: content,
      ttl: parseInt(ttl),
    };

    // Add host parameter if name is not root domain
    if (name && name !== "@" && name !== "") {
      apiParams.host = name;
    }

    // Determine API endpoint based on record type
    switch (type.toUpperCase()) {
      case "A":
        apiUrl = `${API_BASE_URL}/dns/manage/add-ipv4-record.json`;
        break;
      case "AAAA":
        apiUrl = `${API_BASE_URL}/dns/manage/add-ipv6-record.json`;
        break;
      case "CNAME":
        apiUrl = `${API_BASE_URL}/dns/manage/add-cname-record.json`;
        break;
      case "MX":
        apiUrl = `${API_BASE_URL}/dns/manage/add-mx-record.json`;
        apiParams.priority = record.priority || 10;
        break;
      case "TXT":
        apiUrl = `${API_BASE_URL}/dns/manage/add-txt-record.json`;
        break;
      case "NS":
        apiUrl = `${API_BASE_URL}/dns/manage/add-ns-record.json`;
        break;
      case "SRV":
        apiUrl = `${API_BASE_URL}/dns/manage/add-srv-record.json`;
        apiParams.priority = record.priority || 10;
        apiParams.weight = record.weight || 5;
        apiParams.port = record.port || 80;
        break;
      default:
        return res.status(400).json({
          success: false,
          error: `Unsupported DNS record type: ${type}`,
        });
    }

    logger.log("🌐 [DNS] API URL:", apiUrl);
    logger.log("🔑 [DNS] API Params:", apiParams);

    // Call the Heberjahiz API to add the DNS record
    logger.log(`🔄 [DNS] Calling Heberjahiz API to add ${type} record for ${domainName}`);
    const response = await axios.post(apiUrl, null, {
      params: apiParams,
      timeout: 15000,
    });

    logger.log("✅ [DNS] Raw API Response Status:", response.status);
    logger.log("✅ [DNS] Raw API Response Data:", JSON.stringify(response.data, null, 2));
    logger.log("✅ [DNS] Response Data Type:", typeof response.data);

    // Check if the response indicates success
    let isSuccess = false;
    if (response.status === 200) {
      if (typeof response.data === "string") {
        isSuccess = response.data.includes("Success") || response.data.includes("success");
      } else if (typeof response.data === "object") {
        isSuccess = response.data.status === "Success" || response.data.status === "success";
      } else {
        isSuccess = true; // HTTP 200 fallback
      }
    }

    if (!isSuccess) {
      logger.error("❌ [DNS] API returned non-success response:", response.data);
      return res.status(500).json({
        success: false,
        error: "Failed to add DNS record - API returned error",
        details: response.data,
      });
    }

    logger.log("✅ [DNS] DNS record added successfully via API");

    // Create response record object
    const newRecord = {
      id: Date.now().toString(), // In real implementation, this might come from the API response
      type,
      name,
      content,
      ttl: parseInt(ttl),
      priority: record.priority ? parseInt(record.priority) : null,
      weight: record.weight ? parseInt(record.weight) : null,
      port: record.port ? parseInt(record.port) : null,
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    res.json({
      success: true,
      record: newRecord,
      apiResponse: response.data,
      message: "DNS record added successfully",
    });

  } catch (error) {
    logger.error("❌ [DNS] Error adding DNS record:", error);
    logger.error("❌ [DNS] Error stack:", error.stack);
    logger.error("❌ [DNS] Error response data:", error.response?.data);
    logger.error("❌ [DNS] Error message:", error.message);

    res.status(500).json({
      success: false,
      error: "Failed to add DNS record",
      details: {
        message: error.message,
        stack: error.stack,
        responseData: error.response?.data,
        code: error.code,
        status: error.response?.status
      },
    });
  }
};

// Update DNS Record
exports.updateDnsRecord = async (req, res) => {
  const logger = require("../../utils/globalLogger")("updateDnsRecord");
  try {
    const { domainId, recordId } = req.params;
    const { record } = req.body;
    const userId = req.user?.id;

    if (!domainId || !recordId || !record) {
      return res.status(400).json({
        success: false,
        error: "Domain ID, record ID, and record data are required",
      });
    }

    logger.log(`✏️ Updating DNS record ${recordId} for domain ID: ${domainId}`, record);

    // Mock implementation - in real scenario, this would:
    // 1. Validate the record data
    // 2. Call reseller API or update in local database
    // 3. Return the updated record

    const updatedRecord = {
      id: recordId,
      ...record,
      ttl: parseInt(record.ttl),
      priority: record.priority ? parseInt(record.priority) : null,
      weight: record.weight ? parseInt(record.weight) : null,
      port: record.port ? parseInt(record.port) : null,
      updatedAt: new Date(),
    };

    res.json({
      success: true,
      record: updatedRecord,
      message: "DNS record updated successfully",
    });

    // TODO: Implement actual DNS record update

  } catch (error) {
    logger.error("❌ Error updating DNS record:", error);
    res.status(500).json({
      success: false,
      error: "Failed to update DNS record",
      details: error.message,
    });
  }
};

// Delete DNS Record
exports.deleteDnsRecord = async (req, res) => {
  const logger = require("../../utils/globalLogger")("deleteDnsRecord");
  try {
    const { domainId, recordId } = req.params;
    const userId = req.user?.id;

    if (!domainId || !recordId) {
      return res.status(400).json({
        success: false,
        error: "Domain ID and record ID are required",
      });
    }

    logger.log(`🗑️ Deleting DNS record ${recordId} for domain ID: ${domainId}`);

    // Mock implementation - in real scenario, this would:
    // 1. Verify the record exists and belongs to the user
    // 2. Call reseller API or delete from local database
    // 3. Return success confirmation

    res.json({
      success: true,
      message: "DNS record deleted successfully",
    });

    // TODO: Implement actual DNS record deletion

  } catch (error) {
    logger.error("❌ Error deleting DNS record:", error);
    res.status(500).json({
      success: false,
      error: "Failed to delete DNS record",
      details: error.message,
    });
  }
};

// Test endpoint to debug domain lookup
exports.testDomainLookup = async (req, res) => {
  const logger = require("../../utils/globalLogger")("testDomainLookup");
  try {
    const { domainId } = req.params;
    const userId = req.user?._id;

    logger.log(`🔍 [TEST] Testing domain lookup for ID: ${domainId}, User: ${userId}`);

    if (!domainId) {
      return res.status(400).json({
        success: false,
        error: "Domain ID is required",
      });
    }

    if (!userId) {
      return res.status(401).json({
        success: false,
        error: "User authentication required",
      });
    }

    // Test the domain lookup function
    const domainName = await getDomainNameFromId(domainId, userId);
    const domainOrderId = await getDomainOrderIdFromId(domainId, userId);

    logger.log(`✅ [TEST] Domain lookup results - Name: ${domainName}, OrderID: ${domainOrderId}`);

    res.json({
      success: true,
      domainId,
      domainName,
      domainOrderId,
      userId,
      message: "Domain lookup test completed",
    });

  } catch (error) {
    logger.error("❌ [TEST] Error in domain lookup test:", error.message);
    res.status(500).json({
      success: false,
      error: "Domain lookup test failed",
      details: error.message,
    });
  }
};
