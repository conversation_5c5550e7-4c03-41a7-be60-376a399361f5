"use client";
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>B<PERSON>, <PERSON><PERSON> } from "@material-tailwind/react";
import { useEffect, useState } from "react";
import { useTranslations } from "next-intl";
import { useRouter } from "next/navigation";
import {
  Globe,
  ExternalLink,
  RefreshCw,
  Lock,
  Shield,
  Server,
  Zap,
} from "lucide-react";
import domainMngService from "@/app/services/domainMngService";
import orderService from "@/app/services/orderService";

export default function DomainManagementPage() {
  const t = useTranslations("client");
  const dt = useTranslations("client.domainWrapper");
  const [domains, setDomains] = useState([]);
  const [loading, setLoading] = useState(true);
  const router = useRouter();

  useEffect(() => {
    const getUserDomains = async () => {
      try {
        setLoading(true);
        // Get real domain data from the API
        const res = await domainMngService.getUserDomains();
        console.log("Domain data:", res.data);

        if (res.data && res.data.domains) {
          setDomains(res.data.domains);
        } else {
          setDomains([]);
        }
        setLoading(false);
      } catch (error) {
        console.error("Error getting domains", error);
        setLoading(false);
        // If there's an error, set domains to empty array
        setDomains([]);
      }
    };
    getUserDomains();
  }, []);

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-pulse flex flex-col items-center">
          <div className="h-12 w-12 bg-blue-100 rounded-full flex items-center justify-center mb-4">
            <Globe className="h-6 w-6 text-blue-600" />
          </div>
          <Typography variant="h6" className="text-gray-600">
            {t("loading")}...
          </Typography>
        </div>
      </div>
    );
  }

  if (!domains?.length) {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen p-8">
        <div className="h-20 w-20 bg-blue-100 rounded-full flex items-center justify-center mb-6">
          <Globe className="h-10 w-10 text-blue-600" />
        </div>
        <Typography variant="h4" className="text-gray-800 font-bold mb-2">
          {dt("no_domains")}
        </Typography>
        <Typography className="text-gray-600 text-center max-w-md mb-8">
          {dt("browse_domains_message")}
        </Typography>
        <Button
          size="lg"
          className="bg-blue-600 hover:bg-blue-700 flex items-center gap-2"
          onClick={() => router.push("/domains")}
        >
          {dt("browse_domains")}
          <ExternalLink className="h-4 w-4" />
        </Button>
      </div>
    );
  }

  return (
    <div className="p-8 bg-gray-50 min-h-screen">
      <div className="max-w-7xl mx-auto">
        <div className="flex justify-between items-center mb-8">
          <div>
            <Typography
              variant="h1"
              className="text-3xl font-bold text-gray-800"
            >
              {dt("your_domains")}
            </Typography>
            <Typography className="text-gray-600 mt-1">
              {dt("manage_domains")}
            </Typography>
          </div>
          <Button
            className="bg-blue-600 hover:bg-blue-700 flex items-center gap-2"
            onClick={() => router.push("/domains")}
          >
            {dt("browse_domains")}
            <ExternalLink className="h-4 w-4" />
          </Button>
        </div>

        <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="bg-gray-50 border-b border-gray-200">
                  <th className="px-6 py-4 text-left text-sm font-medium text-gray-500">
                    {dt("domain_name")}
                  </th>
                  <th className="px-6 py-4 text-left text-sm font-medium text-gray-500">
                    {dt("status")}
                  </th>
                  <th className="px-6 py-4 text-left text-sm font-medium text-gray-500">
                    {dt("registration_date")}
                  </th>
                  <th className="px-6 py-4 text-left text-sm font-medium text-gray-500">
                    {dt("expiry_date")}
                  </th>

                  <th className="px-6 py-4 text-right text-sm font-medium text-gray-500">
                    {dt("manage")}
                  </th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-200">
                {domains.map((domain) => (
                  <tr key={domain.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4">
                      <div className="flex items-center">
                        <div className="h-10 w-10 bg-blue-100 rounded-lg flex items-center justify-center">
                          <Globe className="h-5 w-5 text-blue-600" />
                        </div>
                        <div className="ml-3">
                          <Typography className="font-medium text-gray-900">
                            {domain.name}
                          </Typography>
                          <Typography className="text-sm text-gray-500">
                            {domain.registrar}
                          </Typography>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4">
                      <span
                        className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium capitalize ${domain.status?.toLowerCase() === "active"
                          ? "bg-green-100 text-green-800"
                          : domain.status?.toLowerCase() === "pending"
                            ? "bg-yellow-100 text-yellow-800"
                            : domain.status?.toLowerCase() === "expired"
                              ? "bg-red-100 text-red-800"
                              : domain.status?.toLowerCase() === "failed"
                                ? "bg-red-100 text-red-800"
                                : "bg-gray-100 text-gray-800"
                          }`}
                      >
                        {dt(domain.status?.toLowerCase() || "unknown")}
                      </span>
                    </td>
                    <td className="px-6 py-4 text-sm text-gray-500">
                      {new Date(domain.registrationDate).toLocaleDateString()}
                    </td>
                    <td className="px-6 py-4 text-sm text-gray-500">
                      {new Date(domain.expiryDate).toLocaleDateString()}
                    </td>

                    <td className="px-6 py-4 text-right">
                      <Button
                        size="sm"
                        className="bg-blue-600 hover:bg-blue-700"
                        onClick={() =>
                          router.push(`/client/domains/${domain.id}`)
                        }
                      >
                        {dt("manage")}
                      </Button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>

        <div className="mt-8 grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 hidden">
          <Card className="bg-white rounded-xl shadow-sm border border-gray-200">
            <CardBody className="p-6">
              <div className="flex items-center mb-4">
                <div className="h-10 w-10 bg-blue-100 rounded-lg flex items-center justify-center">
                  <Server className="h-5 w-5 text-blue-600" />
                </div>
                <Typography className="ml-3 font-medium text-gray-900">
                  {dt("dns_settings")}
                </Typography>
              </div>
              <Typography className="text-sm text-gray-600 mb-4">
                {t("manage_dns_records_description", {
                  defaultValue:
                    "Configure DNS records, nameservers, and more for your domains.",
                })}
              </Typography>
              <Button
                variant="outlined"
                className="w-full border-blue-600 text-blue-600 hover:bg-blue-50"
                onClick={() => router.push("/client/domains/dns")}
              >
                {dt("manage_dns_records")}
              </Button>
            </CardBody>
          </Card>

          <Card className="bg-gradient-to-br from-purple-50 to-indigo-50 rounded-xl shadow-sm border border-purple-200">
            <CardBody className="p-6">
              <div className="flex items-center mb-4">
                <div className="h-10 w-10 bg-purple-100 rounded-lg flex items-center justify-center">
                  <Zap className="h-5 w-5 text-purple-600" />
                </div>
                <Typography className="ml-3 font-medium text-gray-900">
                  Modern DNS Manager
                </Typography>
              </div>
              <Typography className="text-sm text-gray-600 mb-4">
                Advanced DNS management with real-time API integration and modern interface.
              </Typography>
              <Button
                variant="outlined"
                className="w-full border-purple-600 text-purple-600 hover:bg-purple-50"
                onClick={() => {
                  if (domains.length > 0) {
                    router.push(`/client/domains/${domains[0].id}/dns`);
                  } else {
                    toast.info("Please register a domain first to access DNS management.");
                  }
                }}
              >
                Try Modern DNS
              </Button>
            </CardBody>
          </Card>

          <Card className="bg-white rounded-xl shadow-sm border border-gray-200">
            <CardBody className="p-6">
              <div className="flex items-center mb-4">
                <div className="h-10 w-10 bg-blue-100 rounded-lg flex items-center justify-center">
                  <Lock className="h-5 w-5 text-blue-600" />
                </div>
                <Typography className="ml-3 font-medium text-gray-900">
                  {dt("domain_locks")}
                </Typography>
              </div>
              <Typography className="text-sm text-gray-600 mb-4">
                {t("domain_locks_description", {
                  defaultValue:
                    "Protect your domains from unauthorized transfers and changes.",
                })}
              </Typography>
              <Button
                variant="outlined"
                className="w-full border-blue-600 text-blue-600 hover:bg-blue-50"
                onClick={() => router.push("/client/domains/locks")}
              >
                {t("manage_locks", { defaultValue: "Manage Locks" })}
              </Button>
            </CardBody>
          </Card>

          <Card className="bg-white rounded-xl shadow-sm border border-gray-200">
            <CardBody className="p-6">
              <div className="flex items-center mb-4">
                <div className="h-10 w-10 bg-blue-100 rounded-lg flex items-center justify-center">
                  <Shield className="h-5 w-5 text-blue-600" />
                </div>
                <Typography className="ml-3 font-medium text-gray-900">
                  {dt("whois_privacy")}
                </Typography>
              </div>
              <Typography className="text-sm text-gray-600 mb-4">
                {t("whois_privacy_description", {
                  defaultValue:
                    "Protect your personal information from being publicly visible in WHOIS records.",
                })}
              </Typography>
              <Button
                variant="outlined"
                className="w-full border-blue-600 text-blue-600 hover:bg-blue-50"
                onClick={() => router.push("/client/domains/privacy")}
              >
                {t("manage_privacy", { defaultValue: "Manage Privacy" })}
              </Button>
            </CardBody>
          </Card>
        </div>
      </div>
    </div>
  );
}
