Activating the DNS Service
Description
Parameters
HTTP Method
Example Test URL Request
Response
Description
Activates the DNS service

Parameters
Name	Data Type	Required / Optional	Description
auth-userid	Integer	Required	Authentication Parameter
api-key	String	Required	Authentication Parameter
order-id	Integer	Required	Order Id of the Order for which the DNS service is to be activated
HTTP Method
POST

Example Test URL Request
https://test.httpapi.com/api/dns/activate.xml?auth-userid=0&api-key=key&order-id=0

Response
Returns a map containing status information.