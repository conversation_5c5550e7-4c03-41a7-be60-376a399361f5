"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/client/domains/[id]/dns/page",{

/***/ "(app-pages-browser)/./src/components/domains/ModernDnsManager.jsx":
/*!*****************************************************!*\
  !*** ./src/components/domains/ModernDnsManager.jsx ***!
  \*****************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ ModernDnsManager; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @material-tailwind/react */ \"(app-pages-browser)/./node_modules/@material-tailwind/react/index.js\");\n/* harmony import */ var _material_tailwind_react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_CheckCircle_Copy_Edit_Globe_Plus_Power_Server_Shield_Trash2_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,CheckCircle,Copy,Edit,Globe,Plus,Power,Server,Shield,Trash2,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_CheckCircle_Copy_Edit_Globe_Plus_Power_Server_Shield_Trash2_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,CheckCircle,Copy,Edit,Globe,Plus,Power,Server,Shield,Trash2,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/server.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_CheckCircle_Copy_Edit_Globe_Plus_Power_Server_Shield_Trash2_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,CheckCircle,Copy,Edit,Globe,Plus,Power,Server,Shield,Trash2,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/wifi.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_CheckCircle_Copy_Edit_Globe_Plus_Power_Server_Shield_Trash2_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,CheckCircle,Copy,Edit,Globe,Plus,Power,Server,Shield,Trash2,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_CheckCircle_Copy_Edit_Globe_Plus_Power_Server_Shield_Trash2_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,CheckCircle,Copy,Edit,Globe,Plus,Power,Server,Shield,Trash2,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_CheckCircle_Copy_Edit_Globe_Plus_Power_Server_Shield_Trash2_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,CheckCircle,Copy,Edit,Globe,Plus,Power,Server,Shield,Trash2,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/power.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_CheckCircle_Copy_Edit_Globe_Plus_Power_Server_Shield_Trash2_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,CheckCircle,Copy,Edit,Globe,Plus,Power,Server,Shield,Trash2,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_CheckCircle_Copy_Edit_Globe_Plus_Power_Server_Shield_Trash2_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,CheckCircle,Copy,Edit,Globe,Plus,Power,Server,Shield,Trash2,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_CheckCircle_Copy_Edit_Globe_Plus_Power_Server_Shield_Trash2_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,CheckCircle,Copy,Edit,Globe,Plus,Power,Server,Shield,Trash2,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_CheckCircle_Copy_Edit_Globe_Plus_Power_Server_Shield_Trash2_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,CheckCircle,Copy,Edit,Globe,Plus,Power,Server,Shield,Trash2,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/copy.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_CheckCircle_Copy_Edit_Globe_Plus_Power_Server_Shield_Trash2_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,CheckCircle,Copy,Edit,Globe,Plus,Power,Server,Shield,Trash2,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_CheckCircle_Copy_Edit_Globe_Plus_Power_Server_Shield_Trash2_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,CheckCircle,Copy,Edit,Globe,Plus,Power,Server,Shield,Trash2,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_CheckCircle_Copy_Edit_Globe_Plus_Power_Server_Shield_Trash2_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,CheckCircle,Copy,Edit,Globe,Plus,Power,Server,Shield,Trash2,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _app_services_domainMngService__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/app/services/domainMngService */ \"(app-pages-browser)/./src/app/services/domainMngService.js\");\n/* harmony import */ var react_toastify__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-toastify */ \"(app-pages-browser)/./node_modules/react-toastify/dist/react-toastify.esm.mjs\");\n/* harmony import */ var react_toastify_dist_ReactToastify_css__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react-toastify/dist/ReactToastify.css */ \"(app-pages-browser)/./node_modules/react-toastify/dist/ReactToastify.css\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nfunction ModernDnsManager(param) {\n    let { domain, onUpdate } = param;\n    _s();\n    const [dnsServiceActive, setDnsServiceActive] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [dnsRecords, setDnsRecords] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [activatingService, setActivatingService] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showAddForm, setShowAddForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedRecordType, setSelectedRecordType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"A\");\n    // Form states for adding records\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: \"@\",\n        content: \"\",\n        ttl: \"3600\"\n    });\n    // Load DNS records\n    const loadDnsRecords = async ()=>{\n        try {\n            setLoading(true);\n            const response = await _app_services_domainMngService__WEBPACK_IMPORTED_MODULE_3__[\"default\"].getDnsRecords(domain.id, {\n                type: selectedRecordType\n            });\n            if (response.data.success) {\n                setDnsRecords(response.data.records || []);\n                setDnsServiceActive(true); // If we can get records, service is active\n            }\n        } catch (error) {\n            console.error(\"Error loading DNS records:\", error);\n            // If we can't get records, service might not be activated\n            setDnsServiceActive(false);\n        } finally{\n            setLoading(false);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (domain === null || domain === void 0 ? void 0 : domain.id) {\n            loadDnsRecords();\n        }\n    }, [\n        domain === null || domain === void 0 ? void 0 : domain.id\n    ]);\n    // Activate DNS Service\n    const activateDnsService = async ()=>{\n        console.log(\"Activating DNS service for domain:\", domain);\n        // Try to get the order ID from various possible fields\n        const orderIdToUse = (domain === null || domain === void 0 ? void 0 : domain.domainOrderId) || (domain === null || domain === void 0 ? void 0 : domain.orderid) || (domain === null || domain === void 0 ? void 0 : domain.orderId) || (domain === null || domain === void 0 ? void 0 : domain.id);\n        if (!orderIdToUse) {\n            react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.error(\"Domain order ID not found. Cannot activate DNS service.\");\n            console.error(\"Domain object:\", domain);\n            return;\n        }\n        console.log(\"Using order ID for DNS activation:\", orderIdToUse);\n        try {\n            setActivatingService(true);\n            const response = await _app_services_domainMngService__WEBPACK_IMPORTED_MODULE_3__[\"default\"].activateDnsService(orderIdToUse);\n            if (response.data.success) {\n                setDnsServiceActive(true);\n                react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.success(\"DNS service activated successfully!\");\n                await loadDnsRecords(); // Reload records after activation\n            } else {\n                throw new Error(response.data.error || \"Failed to activate DNS service\");\n            }\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error(\"Error activating DNS service:\", error);\n            react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.error(\"Failed to activate DNS service: \" + (((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.details) || error.message));\n        } finally{\n            setActivatingService(false);\n        }\n    };\n    // Add DNS Record\n    const addDnsRecord = async ()=>{\n        if (!formData.content.trim()) {\n            react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.error(\"Please enter the record content\");\n            return;\n        }\n        try {\n            setLoading(true);\n            const recordData = {\n                type: selectedRecordType,\n                name: formData.name,\n                content: formData.content,\n                ttl: parseInt(formData.ttl)\n            };\n            const response = await _app_services_domainMngService__WEBPACK_IMPORTED_MODULE_3__[\"default\"].addDnsRecord(domain.id, recordData);\n            if (response.data.success) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.success(\"\".concat(selectedRecordType, \" record added successfully!\"));\n                setFormData({\n                    name: \"@\",\n                    content: \"\",\n                    ttl: \"3600\"\n                });\n                setShowAddForm(false);\n                await loadDnsRecords(); // Reload records\n            } else {\n                throw new Error(response.data.error || \"Failed to add DNS record\");\n            }\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error(\"Error adding DNS record:\", error);\n            react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.error(\"Failed to add DNS record: \" + (((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.details) || error.message));\n        } finally{\n            setLoading(false);\n        }\n    };\n    // Copy to clipboard\n    const copyToClipboard = (text)=>{\n        navigator.clipboard.writeText(text);\n        react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.success(\"Copied to clipboard!\");\n    };\n    // Get record type icon and color\n    const getRecordTypeInfo = (type)=>{\n        const info = {\n            A: {\n                icon: _barrel_optimize_names_Activity_AlertCircle_CheckCircle_Copy_Edit_Globe_Plus_Power_Server_Shield_Trash2_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n                color: \"blue\",\n                bgColor: \"bg-blue-50\",\n                textColor: \"text-blue-700\"\n            },\n            AAAA: {\n                icon: _barrel_optimize_names_Activity_AlertCircle_CheckCircle_Copy_Edit_Globe_Plus_Power_Server_Shield_Trash2_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n                color: \"indigo\",\n                bgColor: \"bg-indigo-50\",\n                textColor: \"text-indigo-700\"\n            },\n            CNAME: {\n                icon: _barrel_optimize_names_Activity_AlertCircle_CheckCircle_Copy_Edit_Globe_Plus_Power_Server_Shield_Trash2_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n                color: \"purple\",\n                bgColor: \"bg-purple-50\",\n                textColor: \"text-purple-700\"\n            },\n            MX: {\n                icon: _barrel_optimize_names_Activity_AlertCircle_CheckCircle_Copy_Edit_Globe_Plus_Power_Server_Shield_Trash2_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n                color: \"green\",\n                bgColor: \"bg-green-50\",\n                textColor: \"text-green-700\"\n            },\n            TXT: {\n                icon: _barrel_optimize_names_Activity_AlertCircle_CheckCircle_Copy_Edit_Globe_Plus_Power_Server_Shield_Trash2_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n                color: \"orange\",\n                bgColor: \"bg-orange-50\",\n                textColor: \"text-orange-700\"\n            },\n            NS: {\n                icon: _barrel_optimize_names_Activity_AlertCircle_CheckCircle_Copy_Edit_Globe_Plus_Power_Server_Shield_Trash2_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n                color: \"gray\",\n                bgColor: \"bg-gray-50\",\n                textColor: \"text-gray-700\"\n            },\n            SRV: {\n                icon: _barrel_optimize_names_Activity_AlertCircle_CheckCircle_Copy_Edit_Globe_Plus_Power_Server_Shield_Trash2_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n                color: \"pink\",\n                bgColor: \"bg-pink-50\",\n                textColor: \"text-pink-700\"\n            }\n        };\n        return info[type] || info.A;\n    };\n    // Format record name for display\n    const formatRecordName = (name)=>{\n        if (name === \"@\" || name === \"\") {\n            return \"@ (\".concat(domain === null || domain === void 0 ? void 0 : domain.name, \")\");\n        }\n        return \"\".concat(name, \".\").concat(domain === null || domain === void 0 ? void 0 : domain.name);\n    };\n    // Available record types (only show implemented ones)\n    const availableRecordTypes = [\n        {\n            value: \"A\",\n            label: \"A Record\",\n            description: \"IPv4 Address\",\n            implemented: true\n        },\n        {\n            value: \"AAAA\",\n            label: \"AAAA Record\",\n            description: \"IPv6 Address\",\n            implemented: true\n        },\n        {\n            value: \"CNAME\",\n            label: \"CNAME Record\",\n            description: \"Domain Alias\",\n            implemented: false\n        },\n        {\n            value: \"MX\",\n            label: \"MX Record\",\n            description: \"Mail Server\",\n            implemented: false\n        },\n        {\n            value: \"TXT\",\n            label: \"TXT Record\",\n            description: \"Text Data\",\n            implemented: false\n        },\n        {\n            value: \"NS\",\n            label: \"NS Record\",\n            description: \"Name Server\",\n            implemented: false\n        },\n        {\n            value: \"SRV\",\n            label: \"SRV Record\",\n            description: \"Service Location\",\n            implemented: false\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                className: \"border-l-4 border-l-blue-500\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.CardBody, {\n                    className: \"p-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-3 rounded-full \".concat(dnsServiceActive ? \"bg-green-100\" : \"bg-gray-100\"),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_CheckCircle_Copy_Edit_Globe_Plus_Power_Server_Shield_Trash2_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            className: \"h-6 w-6 \".concat(dnsServiceActive ? \"text-green-600\" : \"text-gray-400\")\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                            lineNumber: 276,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                        lineNumber: 271,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Typography, {\n                                                variant: \"h5\",\n                                                className: \"text-gray-800 mb-1\",\n                                                children: \"DNS Service Status\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                                lineNumber: 283,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Chip, {\n                                                        value: dnsServiceActive ? \"Active\" : \"Inactive\",\n                                                        color: dnsServiceActive ? \"green\" : \"gray\",\n                                                        className: \"text-xs\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                                        lineNumber: 287,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Typography, {\n                                                        className: \"text-sm text-gray-600\",\n                                                        children: dnsServiceActive ? \"DNS service is active and ready to manage records\" : \"DNS service needs to be activated before managing records\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                                        lineNumber: 292,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                                lineNumber: 286,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                        lineNumber: 282,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                lineNumber: 270,\n                                columnNumber: 13\n                            }, this),\n                            !dnsServiceActive && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                className: \"bg-blue-600 hover:bg-blue-700 flex items-center gap-2\",\n                                onClick: activateDnsService,\n                                disabled: activatingService,\n                                children: [\n                                    activatingService ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-white\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                        lineNumber: 308,\n                                        columnNumber: 19\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_CheckCircle_Copy_Edit_Globe_Plus_Power_Server_Shield_Trash2_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                        lineNumber: 310,\n                                        columnNumber: 19\n                                    }, this),\n                                    \"Activate DNS Service\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                lineNumber: 302,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                        lineNumber: 269,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                    lineNumber: 268,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                lineNumber: 267,\n                columnNumber: 7\n            }, this),\n            dnsServiceActive && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.CardBody, {\n                        className: \"p-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Typography, {\n                                                variant: \"h5\",\n                                                className: \"text-gray-800 mb-1\",\n                                                children: \"DNS Records\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                                lineNumber: 327,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Typography, {\n                                                className: \"text-sm text-gray-600\",\n                                                children: \"Manage your domain's DNS records\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                                lineNumber: 330,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                        lineNumber: 326,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        className: \"bg-blue-600 hover:bg-blue-700 flex items-center gap-2\",\n                                        onClick: ()=>setShowAddForm(!showAddForm),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_CheckCircle_Copy_Edit_Globe_Plus_Power_Server_Shield_Trash2_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                className: \"h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                                lineNumber: 338,\n                                                columnNumber: 19\n                                            }, this),\n                                            \"Add Record\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                        lineNumber: 334,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                lineNumber: 325,\n                                columnNumber: 15\n                            }, this),\n                            showAddForm && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gray-50 p-6 rounded-lg mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Typography, {\n                                        variant: \"h6\",\n                                        className: \"text-gray-800 mb-4\",\n                                        children: \"Add New DNS Record\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                        lineNumber: 346,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Select, {\n                                                label: \"Record Type\",\n                                                value: selectedRecordType,\n                                                onChange: (val)=>setSelectedRecordType(val),\n                                                children: availableRecordTypes.map((type)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Option, {\n                                                        value: type.value,\n                                                        disabled: !type.implemented,\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between w-full\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: type.label\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                                                    lineNumber: 363,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                !type.implemented && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Chip, {\n                                                                    value: \"Soon\",\n                                                                    size: \"sm\",\n                                                                    color: \"amber\",\n                                                                    className: \"text-xs\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                                                    lineNumber: 365,\n                                                                    columnNumber: 31\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                                            lineNumber: 362,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, type.value, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                                        lineNumber: 357,\n                                                        columnNumber: 25\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                                lineNumber: 351,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Input, {\n                                                label: \"Name\",\n                                                value: formData.name,\n                                                onChange: (e)=>setFormData((prev)=>({\n                                                            ...prev,\n                                                            name: e.target.value\n                                                        })),\n                                                placeholder: \"@, www, mail, etc.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                                lineNumber: 377,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Input, {\n                                                label: selectedRecordType === \"A\" ? \"IPv4 Address\" : \"IPv6 Address\",\n                                                value: formData.content,\n                                                onChange: (e)=>setFormData((prev)=>({\n                                                            ...prev,\n                                                            content: e.target.value\n                                                        })),\n                                                placeholder: selectedRecordType === \"A\" ? \"***********\" : \"2001:db8::1\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                                lineNumber: 389,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Select, {\n                                                label: \"TTL\",\n                                                value: formData.ttl,\n                                                onChange: (val)=>setFormData((prev)=>({\n                                                            ...prev,\n                                                            ttl: val\n                                                        })),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Option, {\n                                                        value: \"300\",\n                                                        children: \"5 minutes\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                                        lineNumber: 416,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Option, {\n                                                        value: \"1800\",\n                                                        children: \"30 minutes\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                                        lineNumber: 417,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Option, {\n                                                        value: \"3600\",\n                                                        children: \"1 hour\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                                        lineNumber: 418,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Option, {\n                                                        value: \"14400\",\n                                                        children: \"4 hours\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                                        lineNumber: 419,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Option, {\n                                                        value: \"86400\",\n                                                        children: \"1 day\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                                        lineNumber: 420,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                                lineNumber: 409,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                        lineNumber: 350,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex gap-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                className: \"bg-green-600 hover:bg-green-700 flex items-center gap-2\",\n                                                onClick: addDnsRecord,\n                                                disabled: loading,\n                                                children: [\n                                                    loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-white\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                                        lineNumber: 431,\n                                                        columnNumber: 25\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_CheckCircle_Copy_Edit_Globe_Plus_Power_Server_Shield_Trash2_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                        className: \"h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                                        lineNumber: 433,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    \"Add Record\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                                lineNumber: 425,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                variant: \"outlined\",\n                                                onClick: ()=>setShowAddForm(false),\n                                                children: \"Cancel\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                                lineNumber: 437,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                        lineNumber: 424,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                lineNumber: 345,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-3\",\n                                children: loading && dnsRecords.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center py-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                            lineNumber: 451,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Typography, {\n                                            className: \"text-gray-600\",\n                                            children: \"Loading DNS records...\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                            lineNumber: 452,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                    lineNumber: 450,\n                                    columnNumber: 19\n                                }, this) : dnsRecords.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center py-12 bg-gray-50 rounded-lg\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_CheckCircle_Copy_Edit_Globe_Plus_Power_Server_Shield_Trash2_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                            className: \"h-12 w-12 text-gray-400 mx-auto mb-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                            lineNumber: 458,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Typography, {\n                                            variant: \"h6\",\n                                            className: \"text-gray-600 mb-2\",\n                                            children: \"No DNS Records Found\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                            lineNumber: 459,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Typography, {\n                                            className: \"text-sm text-gray-500 mb-4\",\n                                            children: \"Start by adding your first DNS record to configure your domain.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                            lineNumber: 462,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                            className: \"bg-blue-600 hover:bg-blue-700\",\n                                            onClick: ()=>setShowAddForm(true),\n                                            children: \"Add Your First Record\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                            lineNumber: 466,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                    lineNumber: 457,\n                                    columnNumber: 19\n                                }, this) : dnsRecords.map((record)=>{\n                                    const typeInfo = getRecordTypeInfo(record.type);\n                                    const IconComponent = typeInfo.icon;\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                        className: \"border border-gray-200 hover:shadow-md transition-shadow\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.CardBody, {\n                                            className: \"p-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-4 flex-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"p-2 rounded-lg \".concat(typeInfo.bgColor),\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(IconComponent, {\n                                                                    className: \"h-5 w-5 \".concat(typeInfo.textColor)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                                                    lineNumber: 489,\n                                                                    columnNumber: 33\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                                                lineNumber: 486,\n                                                                columnNumber: 31\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center gap-3 mb-1\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Chip, {\n                                                                                value: record.type,\n                                                                                color: typeInfo.color,\n                                                                                className: \"text-xs\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                                                                lineNumber: 496,\n                                                                                columnNumber: 35\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Typography, {\n                                                                                className: \"font-medium text-gray-900\",\n                                                                                children: formatRecordName(record.name)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                                                                lineNumber: 501,\n                                                                                columnNumber: 35\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Typography, {\n                                                                                className: \"text-xs text-gray-500\",\n                                                                                children: [\n                                                                                    \"TTL: \",\n                                                                                    record.ttl,\n                                                                                    \"s\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                                                                lineNumber: 504,\n                                                                                columnNumber: 35\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                                                        lineNumber: 495,\n                                                                        columnNumber: 33\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center gap-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Typography, {\n                                                                                className: \"font-mono text-sm text-gray-700\",\n                                                                                children: record.content\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                                                                lineNumber: 510,\n                                                                                columnNumber: 35\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Tooltip, {\n                                                                                content: \"Copy to clipboard\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.IconButton, {\n                                                                                    variant: \"text\",\n                                                                                    size: \"sm\",\n                                                                                    onClick: ()=>copyToClipboard(record.content),\n                                                                                    className: \"p-1\",\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_CheckCircle_Copy_Edit_Globe_Plus_Power_Server_Shield_Trash2_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                                        className: \"h-3 w-3\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                                                                        lineNumber: 522,\n                                                                                        columnNumber: 39\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                                                                    lineNumber: 514,\n                                                                                    columnNumber: 37\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                                                                lineNumber: 513,\n                                                                                columnNumber: 35\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                                                        lineNumber: 509,\n                                                                        columnNumber: 33\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                                                lineNumber: 494,\n                                                                columnNumber: 31\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                                        lineNumber: 485,\n                                                        columnNumber: 29\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Tooltip, {\n                                                                content: \"Edit record\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.IconButton, {\n                                                                    variant: \"text\",\n                                                                    size: \"sm\",\n                                                                    className: \"text-blue-600 hover:bg-blue-50\",\n                                                                    disabled: true,\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_CheckCircle_Copy_Edit_Globe_Plus_Power_Server_Shield_Trash2_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                        className: \"h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                                                        lineNumber: 537,\n                                                                        columnNumber: 35\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                                                    lineNumber: 531,\n                                                                    columnNumber: 33\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                                                lineNumber: 530,\n                                                                columnNumber: 31\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Tooltip, {\n                                                                content: \"Delete record\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.IconButton, {\n                                                                    variant: \"text\",\n                                                                    size: \"sm\",\n                                                                    className: \"text-red-600 hover:bg-red-50\",\n                                                                    disabled: true,\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_CheckCircle_Copy_Edit_Globe_Plus_Power_Server_Shield_Trash2_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                        className: \"h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                                                        lineNumber: 547,\n                                                                        columnNumber: 35\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                                                    lineNumber: 541,\n                                                                    columnNumber: 33\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                                                lineNumber: 540,\n                                                                columnNumber: 31\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                                        lineNumber: 529,\n                                                        columnNumber: 29\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                                lineNumber: 484,\n                                                columnNumber: 27\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                            lineNumber: 483,\n                                            columnNumber: 25\n                                        }, this)\n                                    }, record.id, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                        lineNumber: 479,\n                                        columnNumber: 23\n                                    }, this);\n                                })\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                lineNumber: 448,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                        lineNumber: 324,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                    lineNumber: 323,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Alert, {\n                color: \"blue\",\n                className: \"py-3\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_CheckCircle_Copy_Edit_Globe_Plus_Power_Server_Shield_Trash2_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                        className: \"h-4 w-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                        lineNumber: 565,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Typography, {\n                                className: \"font-medium mb-1\",\n                                children: \"DNS Management Information\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                lineNumber: 567,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                className: \"text-sm space-y-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: \"• Currently supporting A and AAAA record types\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                        lineNumber: 571,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: \"• Additional record types (CNAME, MX, TXT, NS, SRV) coming soon\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                        lineNumber: 572,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: \"• DNS changes may take 24-48 hours to propagate globally\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                        lineNumber: 575,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: \"• Always backup your DNS configuration before making changes\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                        lineNumber: 576,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                lineNumber: 570,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                        lineNumber: 566,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                lineNumber: 564,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n        lineNumber: 265,\n        columnNumber: 5\n    }, this);\n}\n_s(ModernDnsManager, \"/VsvIaj410JVfwrsR+Vaatw4hPY=\");\n_c = ModernDnsManager;\nvar _c;\n$RefreshReg$(_c, \"ModernDnsManager\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL2RvbWFpbnMvTW9kZXJuRG5zTWFuYWdlci5qc3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQzRDO0FBYVY7QUFlWjtBQUN5QztBQUNSO0FBQ1I7QUFFaEMsU0FBUzZCLGlCQUFpQixLQUFvQjtRQUFwQixFQUFFQyxNQUFNLEVBQUVDLFFBQVEsRUFBRSxHQUFwQjs7SUFDdkMsTUFBTSxDQUFDQyxrQkFBa0JDLG9CQUFvQixHQUFHakMsK0NBQVFBLENBQUM7SUFDekQsTUFBTSxDQUFDa0MsWUFBWUMsY0FBYyxHQUFHbkMsK0NBQVFBLENBQUMsRUFBRTtJQUMvQyxNQUFNLENBQUNvQyxTQUFTQyxXQUFXLEdBQUdyQywrQ0FBUUEsQ0FBQztJQUN2QyxNQUFNLENBQUNzQyxtQkFBbUJDLHFCQUFxQixHQUFHdkMsK0NBQVFBLENBQUM7SUFDM0QsTUFBTSxDQUFDd0MsYUFBYUMsZUFBZSxHQUFHekMsK0NBQVFBLENBQUM7SUFDL0MsTUFBTSxDQUFDMEMsb0JBQW9CQyxzQkFBc0IsR0FBRzNDLCtDQUFRQSxDQUFDO0lBRTdELGlDQUFpQztJQUNqQyxNQUFNLENBQUM0QyxVQUFVQyxZQUFZLEdBQUc3QywrQ0FBUUEsQ0FBQztRQUN2QzhDLE1BQU07UUFDTkMsU0FBUztRQUNUQyxLQUFLO0lBQ1A7SUFFQSxtQkFBbUI7SUFDbkIsTUFBTUMsaUJBQWlCO1FBQ3JCLElBQUk7WUFDRlosV0FBVztZQUNYLE1BQU1hLFdBQVcsTUFBTXhCLHNFQUFnQkEsQ0FBQ3lCLGFBQWEsQ0FBQ3JCLE9BQU9zQixFQUFFLEVBQUU7Z0JBQy9EQyxNQUFNWDtZQUNSO1lBQ0EsSUFBSVEsU0FBU0ksSUFBSSxDQUFDQyxPQUFPLEVBQUU7Z0JBQ3pCcEIsY0FBY2UsU0FBU0ksSUFBSSxDQUFDRSxPQUFPLElBQUksRUFBRTtnQkFDekN2QixvQkFBb0IsT0FBTywyQ0FBMkM7WUFDeEU7UUFDRixFQUFFLE9BQU93QixPQUFPO1lBQ2RDLFFBQVFELEtBQUssQ0FBQyw4QkFBOEJBO1lBQzVDLDBEQUEwRDtZQUMxRHhCLG9CQUFvQjtRQUN0QixTQUFVO1lBQ1JJLFdBQVc7UUFDYjtJQUNGO0lBRUFwQyxnREFBU0EsQ0FBQztRQUNSLElBQUk2QixtQkFBQUEsNkJBQUFBLE9BQVFzQixFQUFFLEVBQUU7WUFDZEg7UUFDRjtJQUNGLEdBQUc7UUFBQ25CLG1CQUFBQSw2QkFBQUEsT0FBUXNCLEVBQUU7S0FBQztJQUVmLHVCQUF1QjtJQUN2QixNQUFNTyxxQkFBcUI7UUFDekJELFFBQVFFLEdBQUcsQ0FBQyxzQ0FBc0M5QjtRQUVsRCx1REFBdUQ7UUFDdkQsTUFBTStCLGVBQWUvQixDQUFBQSxtQkFBQUEsNkJBQUFBLE9BQVFnQyxhQUFhLE1BQUloQyxtQkFBQUEsNkJBQUFBLE9BQVFpQyxPQUFPLE1BQUlqQyxtQkFBQUEsNkJBQUFBLE9BQVFrQyxPQUFPLE1BQUlsQyxtQkFBQUEsNkJBQUFBLE9BQVFzQixFQUFFO1FBRTlGLElBQUksQ0FBQ1MsY0FBYztZQUNqQmxDLGlEQUFLQSxDQUFDOEIsS0FBSyxDQUFDO1lBQ1pDLFFBQVFELEtBQUssQ0FBQyxrQkFBa0IzQjtZQUNoQztRQUNGO1FBRUE0QixRQUFRRSxHQUFHLENBQUMsc0NBQXNDQztRQUVsRCxJQUFJO1lBQ0Z0QixxQkFBcUI7WUFDckIsTUFBTVcsV0FBVyxNQUFNeEIsc0VBQWdCQSxDQUFDaUMsa0JBQWtCLENBQUNFO1lBRTNELElBQUlYLFNBQVNJLElBQUksQ0FBQ0MsT0FBTyxFQUFFO2dCQUN6QnRCLG9CQUFvQjtnQkFDcEJOLGlEQUFLQSxDQUFDNEIsT0FBTyxDQUFDO2dCQUNkLE1BQU1OLGtCQUFrQixrQ0FBa0M7WUFDNUQsT0FBTztnQkFDTCxNQUFNLElBQUlnQixNQUNSZixTQUFTSSxJQUFJLENBQUNHLEtBQUssSUFBSTtZQUUzQjtRQUNGLEVBQUUsT0FBT0EsT0FBTztnQkFJVEEsc0JBQUFBO1lBSExDLFFBQVFELEtBQUssQ0FBQyxpQ0FBaUNBO1lBQy9DOUIsaURBQUtBLENBQUM4QixLQUFLLENBQ1QscUNBQ0dBLENBQUFBLEVBQUFBLGtCQUFBQSxNQUFNUCxRQUFRLGNBQWRPLHVDQUFBQSx1QkFBQUEsZ0JBQWdCSCxJQUFJLGNBQXBCRywyQ0FBQUEscUJBQXNCUyxPQUFPLEtBQUlULE1BQU1VLE9BQU87UUFFckQsU0FBVTtZQUNSNUIscUJBQXFCO1FBQ3ZCO0lBQ0Y7SUFFQSxpQkFBaUI7SUFDakIsTUFBTTZCLGVBQWU7UUFDbkIsSUFBSSxDQUFDeEIsU0FBU0csT0FBTyxDQUFDc0IsSUFBSSxJQUFJO1lBQzVCMUMsaURBQUtBLENBQUM4QixLQUFLLENBQUM7WUFDWjtRQUNGO1FBRUEsSUFBSTtZQUNGcEIsV0FBVztZQUNYLE1BQU1pQyxhQUFhO2dCQUNqQmpCLE1BQU1YO2dCQUNOSSxNQUFNRixTQUFTRSxJQUFJO2dCQUNuQkMsU0FBU0gsU0FBU0csT0FBTztnQkFDekJDLEtBQUt1QixTQUFTM0IsU0FBU0ksR0FBRztZQUM1QjtZQUVBLE1BQU1FLFdBQVcsTUFBTXhCLHNFQUFnQkEsQ0FBQzBDLFlBQVksQ0FDbER0QyxPQUFPc0IsRUFBRSxFQUNUa0I7WUFHRixJQUFJcEIsU0FBU0ksSUFBSSxDQUFDQyxPQUFPLEVBQUU7Z0JBQ3pCNUIsaURBQUtBLENBQUM0QixPQUFPLENBQUMsR0FBc0IsT0FBbkJiLG9CQUFtQjtnQkFDcENHLFlBQVk7b0JBQUVDLE1BQU07b0JBQUtDLFNBQVM7b0JBQUlDLEtBQUs7Z0JBQU87Z0JBQ2xEUCxlQUFlO2dCQUNmLE1BQU1RLGtCQUFrQixpQkFBaUI7WUFDM0MsT0FBTztnQkFDTCxNQUFNLElBQUlnQixNQUFNZixTQUFTSSxJQUFJLENBQUNHLEtBQUssSUFBSTtZQUN6QztRQUNGLEVBQUUsT0FBT0EsT0FBTztnQkFJVEEsc0JBQUFBO1lBSExDLFFBQVFELEtBQUssQ0FBQyw0QkFBNEJBO1lBQzFDOUIsaURBQUtBLENBQUM4QixLQUFLLENBQ1QsK0JBQ0dBLENBQUFBLEVBQUFBLGtCQUFBQSxNQUFNUCxRQUFRLGNBQWRPLHVDQUFBQSx1QkFBQUEsZ0JBQWdCSCxJQUFJLGNBQXBCRywyQ0FBQUEscUJBQXNCUyxPQUFPLEtBQUlULE1BQU1VLE9BQU87UUFFckQsU0FBVTtZQUNSOUIsV0FBVztRQUNiO0lBQ0Y7SUFFQSxvQkFBb0I7SUFDcEIsTUFBTW1DLGtCQUFrQixDQUFDQztRQUN2QkMsVUFBVUMsU0FBUyxDQUFDQyxTQUFTLENBQUNIO1FBQzlCOUMsaURBQUtBLENBQUM0QixPQUFPLENBQUM7SUFDaEI7SUFFQSxpQ0FBaUM7SUFDakMsTUFBTXNCLG9CQUFvQixDQUFDeEI7UUFDekIsTUFBTXlCLE9BQU87WUFDWEMsR0FBRztnQkFDREMsTUFBTWxFLHFLQUFLQTtnQkFDWG1FLE9BQU87Z0JBQ1BDLFNBQVM7Z0JBQ1RDLFdBQVc7WUFDYjtZQUNBQyxNQUFNO2dCQUNKSixNQUFNbEUscUtBQUtBO2dCQUNYbUUsT0FBTztnQkFDUEMsU0FBUztnQkFDVEMsV0FBVztZQUNiO1lBQ0FFLE9BQU87Z0JBQ0xMLE1BQU1qRSxxS0FBTUE7Z0JBQ1prRSxPQUFPO2dCQUNQQyxTQUFTO2dCQUNUQyxXQUFXO1lBQ2I7WUFDQUcsSUFBSTtnQkFDRk4sTUFBTXpELHFLQUFJQTtnQkFDVjBELE9BQU87Z0JBQ1BDLFNBQVM7Z0JBQ1RDLFdBQVc7WUFDYjtZQUNBSSxLQUFLO2dCQUNIUCxNQUFNeEQscUtBQU1BO2dCQUNaeUQsT0FBTztnQkFDUEMsU0FBUztnQkFDVEMsV0FBVztZQUNiO1lBQ0FLLElBQUk7Z0JBQ0ZSLE1BQU12RCxzS0FBUUE7Z0JBQ2R3RCxPQUFPO2dCQUNQQyxTQUFTO2dCQUNUQyxXQUFXO1lBQ2I7WUFDQU0sS0FBSztnQkFDSFQsTUFBTWpFLHFLQUFNQTtnQkFDWmtFLE9BQU87Z0JBQ1BDLFNBQVM7Z0JBQ1RDLFdBQVc7WUFDYjtRQUNGO1FBQ0EsT0FBT0wsSUFBSSxDQUFDekIsS0FBSyxJQUFJeUIsS0FBS0MsQ0FBQztJQUM3QjtJQUVBLGlDQUFpQztJQUNqQyxNQUFNVyxtQkFBbUIsQ0FBQzVDO1FBQ3hCLElBQUlBLFNBQVMsT0FBT0EsU0FBUyxJQUFJO1lBQy9CLE9BQU8sTUFBbUIsT0FBYmhCLG1CQUFBQSw2QkFBQUEsT0FBUWdCLElBQUksRUFBQztRQUM1QjtRQUNBLE9BQU8sVUFBR0EsTUFBSyxLQUFnQixPQUFiaEIsbUJBQUFBLDZCQUFBQSxPQUFRZ0IsSUFBSTtJQUNoQztJQUVBLHNEQUFzRDtJQUN0RCxNQUFNNkMsdUJBQXVCO1FBQzNCO1lBQ0VDLE9BQU87WUFDUEMsT0FBTztZQUNQQyxhQUFhO1lBQ2JDLGFBQWE7UUFDZjtRQUNBO1lBQ0VILE9BQU87WUFDUEMsT0FBTztZQUNQQyxhQUFhO1lBQ2JDLGFBQWE7UUFDZjtRQUNBO1lBQ0VILE9BQU87WUFDUEMsT0FBTztZQUNQQyxhQUFhO1lBQ2JDLGFBQWE7UUFDZjtRQUNBO1lBQ0VILE9BQU87WUFDUEMsT0FBTztZQUNQQyxhQUFhO1lBQ2JDLGFBQWE7UUFDZjtRQUNBO1lBQ0VILE9BQU87WUFDUEMsT0FBTztZQUNQQyxhQUFhO1lBQ2JDLGFBQWE7UUFDZjtRQUNBO1lBQ0VILE9BQU87WUFDUEMsT0FBTztZQUNQQyxhQUFhO1lBQ2JDLGFBQWE7UUFDZjtRQUNBO1lBQ0VILE9BQU87WUFDUEMsT0FBTztZQUNQQyxhQUFhO1lBQ2JDLGFBQWE7UUFDZjtLQUNEO0lBRUQscUJBQ0UsOERBQUNDO1FBQUlDLFdBQVU7OzBCQUViLDhEQUFDOUYsMERBQUlBO2dCQUFDOEYsV0FBVTswQkFDZCw0RUFBQzdGLDhEQUFRQTtvQkFBQzZGLFdBQVU7OEJBQ2xCLDRFQUFDRDt3QkFBSUMsV0FBVTs7MENBQ2IsOERBQUNEO2dDQUFJQyxXQUFVOztrREFDYiw4REFBQ0Q7d0NBQ0NDLFdBQVcsb0JBRVYsT0FEQ2pFLG1CQUFtQixpQkFBaUI7a0RBR3RDLDRFQUFDVixzS0FBS0E7NENBQ0oyRSxXQUFXLFdBRVYsT0FEQ2pFLG1CQUFtQixtQkFBbUI7Ozs7Ozs7Ozs7O2tEQUk1Qyw4REFBQ2dFOzswREFDQyw4REFBQzlGLGdFQUFVQTtnREFBQ2dHLFNBQVE7Z0RBQUtELFdBQVU7MERBQXFCOzs7Ozs7MERBR3hELDhEQUFDRDtnREFBSUMsV0FBVTs7a0VBQ2IsOERBQUN2RiwwREFBSUE7d0RBQ0hrRixPQUFPNUQsbUJBQW1CLFdBQVc7d0RBQ3JDaUQsT0FBT2pELG1CQUFtQixVQUFVO3dEQUNwQ2lFLFdBQVU7Ozs7OztrRUFFWiw4REFBQy9GLGdFQUFVQTt3REFBQytGLFdBQVU7a0VBQ25CakUsbUJBQ0csc0RBQ0E7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs0QkFNWCxDQUFDQSxrQ0FDQSw4REFBQzNCLDREQUFNQTtnQ0FDTDRGLFdBQVU7Z0NBQ1ZFLFNBQVN4QztnQ0FDVHlDLFVBQVU5RDs7b0NBRVRBLGtDQUNDLDhEQUFDMEQ7d0NBQUlDLFdBQVU7Ozs7OzZEQUVmLDhEQUFDakYsc0tBQUdBO3dDQUFDaUYsV0FBVTs7Ozs7O29DQUNmOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztZQVNYakUsa0NBQ0M7MEJBRUUsNEVBQUM3QiwwREFBSUE7OEJBQ0gsNEVBQUNDLDhEQUFRQTt3QkFBQzZGLFdBQVU7OzBDQUNsQiw4REFBQ0Q7Z0NBQUlDLFdBQVU7O2tEQUNiLDhEQUFDRDs7MERBQ0MsOERBQUM5RixnRUFBVUE7Z0RBQUNnRyxTQUFRO2dEQUFLRCxXQUFVOzBEQUFxQjs7Ozs7OzBEQUd4RCw4REFBQy9GLGdFQUFVQTtnREFBQytGLFdBQVU7MERBQXdCOzs7Ozs7Ozs7Ozs7a0RBSWhELDhEQUFDNUYsNERBQU1BO3dDQUNMNEYsV0FBVTt3Q0FDVkUsU0FBUyxJQUFNMUQsZUFBZSxDQUFDRDs7MERBRS9CLDhEQUFDM0Isc0tBQUlBO2dEQUFDb0YsV0FBVTs7Ozs7OzRDQUFZOzs7Ozs7Ozs7Ozs7OzRCQU0vQnpELDZCQUNDLDhEQUFDd0Q7Z0NBQUlDLFdBQVU7O2tEQUNiLDhEQUFDL0YsZ0VBQVVBO3dDQUFDZ0csU0FBUTt3Q0FBS0QsV0FBVTtrREFBcUI7Ozs7OztrREFJeEQsOERBQUNEO3dDQUFJQyxXQUFVOzswREFDYiw4REFBQzFGLDREQUFNQTtnREFDTHNGLE9BQU07Z0RBQ05ELE9BQU9sRDtnREFDUDJELFVBQVUsQ0FBQ0MsTUFBUTNELHNCQUFzQjJEOzBEQUV4Q1gscUJBQXFCWSxHQUFHLENBQUMsQ0FBQ2xELHFCQUN6Qiw4REFBQzdDLDREQUFNQTt3REFFTG9GLE9BQU92QyxLQUFLdUMsS0FBSzt3REFDakJRLFVBQVUsQ0FBQy9DLEtBQUswQyxXQUFXO2tFQUUzQiw0RUFBQ0M7NERBQUlDLFdBQVU7OzhFQUNiLDhEQUFDTzs4RUFBTW5ELEtBQUt3QyxLQUFLOzs7Ozs7Z0VBQ2hCLENBQUN4QyxLQUFLMEMsV0FBVyxrQkFDaEIsOERBQUNyRiwwREFBSUE7b0VBQ0hrRixPQUFNO29FQUNOYSxNQUFLO29FQUNMeEIsT0FBTTtvRUFDTmdCLFdBQVU7Ozs7Ozs7Ozs7Ozt1REFYWDVDLEtBQUt1QyxLQUFLOzs7Ozs7Ozs7OzBEQW1CckIsOERBQUN0RiwyREFBS0E7Z0RBQ0p1RixPQUFNO2dEQUNORCxPQUFPaEQsU0FBU0UsSUFBSTtnREFDcEJ1RCxVQUFVLENBQUNLLElBQ1Q3RCxZQUFZLENBQUM4RCxPQUFVOzREQUNyQixHQUFHQSxJQUFJOzREQUNQN0QsTUFBTTRELEVBQUVFLE1BQU0sQ0FBQ2hCLEtBQUs7d0RBQ3RCO2dEQUVGaUIsYUFBWTs7Ozs7OzBEQUdkLDhEQUFDdkcsMkRBQUtBO2dEQUNKdUYsT0FDRW5ELHVCQUF1QixNQUNuQixpQkFDQTtnREFFTmtELE9BQU9oRCxTQUFTRyxPQUFPO2dEQUN2QnNELFVBQVUsQ0FBQ0ssSUFDVDdELFlBQVksQ0FBQzhELE9BQVU7NERBQ3JCLEdBQUdBLElBQUk7NERBQ1A1RCxTQUFTMkQsRUFBRUUsTUFBTSxDQUFDaEIsS0FBSzt3REFDekI7Z0RBRUZpQixhQUNFbkUsdUJBQXVCLE1BQ25CLGdCQUNBOzs7Ozs7MERBSVIsOERBQUNuQyw0REFBTUE7Z0RBQ0xzRixPQUFNO2dEQUNORCxPQUFPaEQsU0FBU0ksR0FBRztnREFDbkJxRCxVQUFVLENBQUNDLE1BQ1R6RCxZQUFZLENBQUM4RCxPQUFVOzREQUFFLEdBQUdBLElBQUk7NERBQUUzRCxLQUFLc0Q7d0RBQUk7O2tFQUc3Qyw4REFBQzlGLDREQUFNQTt3REFBQ29GLE9BQU07a0VBQU07Ozs7OztrRUFDcEIsOERBQUNwRiw0REFBTUE7d0RBQUNvRixPQUFNO2tFQUFPOzs7Ozs7a0VBQ3JCLDhEQUFDcEYsNERBQU1BO3dEQUFDb0YsT0FBTTtrRUFBTzs7Ozs7O2tFQUNyQiw4REFBQ3BGLDREQUFNQTt3REFBQ29GLE9BQU07a0VBQVE7Ozs7OztrRUFDdEIsOERBQUNwRiw0REFBTUE7d0RBQUNvRixPQUFNO2tFQUFROzs7Ozs7Ozs7Ozs7Ozs7Ozs7a0RBSTFCLDhEQUFDSTt3Q0FBSUMsV0FBVTs7MERBQ2IsOERBQUM1Riw0REFBTUE7Z0RBQ0w0RixXQUFVO2dEQUNWRSxTQUFTL0I7Z0RBQ1RnQyxVQUFVaEU7O29EQUVUQSx3QkFDQyw4REFBQzREO3dEQUFJQyxXQUFVOzs7Ozs2RUFFZiw4REFBQ2hGLHNLQUFXQTt3REFBQ2dGLFdBQVU7Ozs7OztvREFDdkI7Ozs7Ozs7MERBR0osOERBQUM1Riw0REFBTUE7Z0RBQ0w2RixTQUFRO2dEQUNSQyxTQUFTLElBQU0xRCxlQUFlOzBEQUMvQjs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBDQVFQLDhEQUFDdUQ7Z0NBQUlDLFdBQVU7MENBQ1o3RCxXQUFXRixXQUFXNEUsTUFBTSxLQUFLLGtCQUNoQyw4REFBQ2Q7b0NBQUlDLFdBQVU7O3NEQUNiLDhEQUFDRDs0Q0FBSUMsV0FBVTs7Ozs7O3NEQUNmLDhEQUFDL0YsZ0VBQVVBOzRDQUFDK0YsV0FBVTtzREFBZ0I7Ozs7Ozs7Ozs7OzJDQUl0Qy9ELFdBQVc0RSxNQUFNLEtBQUssa0JBQ3hCLDhEQUFDZDtvQ0FBSUMsV0FBVTs7c0RBQ2IsOERBQUNuRixxS0FBS0E7NENBQUNtRixXQUFVOzs7Ozs7c0RBQ2pCLDhEQUFDL0YsZ0VBQVVBOzRDQUFDZ0csU0FBUTs0Q0FBS0QsV0FBVTtzREFBcUI7Ozs7OztzREFHeEQsOERBQUMvRixnRUFBVUE7NENBQUMrRixXQUFVO3NEQUE2Qjs7Ozs7O3NEQUluRCw4REFBQzVGLDREQUFNQTs0Q0FDTDRGLFdBQVU7NENBQ1ZFLFNBQVMsSUFBTTFELGVBQWU7c0RBQy9COzs7Ozs7Ozs7OzsyQ0FLSFAsV0FBV3FFLEdBQUcsQ0FBQyxDQUFDUTtvQ0FDZCxNQUFNQyxXQUFXbkMsa0JBQWtCa0MsT0FBTzFELElBQUk7b0NBQzlDLE1BQU00RCxnQkFBZ0JELFNBQVNoQyxJQUFJO29DQUVuQyxxQkFDRSw4REFBQzdFLDBEQUFJQTt3Q0FFSDhGLFdBQVU7a0RBRVYsNEVBQUM3Riw4REFBUUE7NENBQUM2RixXQUFVO3NEQUNsQiw0RUFBQ0Q7Z0RBQUlDLFdBQVU7O2tFQUNiLDhEQUFDRDt3REFBSUMsV0FBVTs7MEVBQ2IsOERBQUNEO2dFQUNDQyxXQUFXLGtCQUFtQyxPQUFqQmUsU0FBUzlCLE9BQU87MEVBRTdDLDRFQUFDK0I7b0VBQ0NoQixXQUFXLFdBQThCLE9BQW5CZSxTQUFTN0IsU0FBUzs7Ozs7Ozs7Ozs7MEVBSTVDLDhEQUFDYTtnRUFBSUMsV0FBVTs7a0ZBQ2IsOERBQUNEO3dFQUFJQyxXQUFVOzswRkFDYiw4REFBQ3ZGLDBEQUFJQTtnRkFDSGtGLE9BQU9tQixPQUFPMUQsSUFBSTtnRkFDbEI0QixPQUFPK0IsU0FBUy9CLEtBQUs7Z0ZBQ3JCZ0IsV0FBVTs7Ozs7OzBGQUVaLDhEQUFDL0YsZ0VBQVVBO2dGQUFDK0YsV0FBVTswRkFDbkJQLGlCQUFpQnFCLE9BQU9qRSxJQUFJOzs7Ozs7MEZBRS9CLDhEQUFDNUMsZ0VBQVVBO2dGQUFDK0YsV0FBVTs7b0ZBQXdCO29GQUN0Q2MsT0FBTy9ELEdBQUc7b0ZBQUM7Ozs7Ozs7Ozs7Ozs7a0ZBSXJCLDhEQUFDZ0Q7d0VBQUlDLFdBQVU7OzBGQUNiLDhEQUFDL0YsZ0VBQVVBO2dGQUFDK0YsV0FBVTswRkFDbkJjLE9BQU9oRSxPQUFPOzs7Ozs7MEZBRWpCLDhEQUFDbkMsNkRBQU9BO2dGQUFDbUMsU0FBUTswRkFDZiw0RUFBQ3BDLGdFQUFVQTtvRkFDVHVGLFNBQVE7b0ZBQ1JPLE1BQUs7b0ZBQ0xOLFNBQVMsSUFDUDNCLGdCQUFnQnVDLE9BQU9oRSxPQUFPO29GQUVoQ2tELFdBQVU7OEZBRVYsNEVBQUM5RSxzS0FBSUE7d0ZBQUM4RSxXQUFVOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O2tFQU8xQiw4REFBQ0Q7d0RBQUlDLFdBQVU7OzBFQUNiLDhEQUFDckYsNkRBQU9BO2dFQUFDbUMsU0FBUTswRUFDZiw0RUFBQ3BDLGdFQUFVQTtvRUFDVHVGLFNBQVE7b0VBQ1JPLE1BQUs7b0VBQ0xSLFdBQVU7b0VBQ1ZHLFFBQVE7OEVBRVIsNEVBQUNoRixzS0FBSUE7d0VBQUM2RSxXQUFVOzs7Ozs7Ozs7Ozs7Ozs7OzBFQUdwQiw4REFBQ3JGLDZEQUFPQTtnRUFBQ21DLFNBQVE7MEVBQ2YsNEVBQUNwQyxnRUFBVUE7b0VBQ1R1RixTQUFRO29FQUNSTyxNQUFLO29FQUNMUixXQUFVO29FQUNWRyxRQUFROzhFQUVSLDRFQUFDL0Usc0tBQU1BO3dFQUFDNEUsV0FBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O3VDQW5FdkJjLE9BQU8zRCxFQUFFOzs7OztnQ0EyRXBCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7MEJBU1osOERBQUMzQywyREFBS0E7Z0JBQUN3RSxPQUFNO2dCQUFPZ0IsV0FBVTs7a0NBQzVCLDhEQUFDL0Usc0tBQVdBO3dCQUFDK0UsV0FBVTs7Ozs7O2tDQUN2Qiw4REFBQ0Q7OzBDQUNDLDhEQUFDOUYsZ0VBQVVBO2dDQUFDK0YsV0FBVTswQ0FBbUI7Ozs7OzswQ0FHekMsOERBQUNpQjtnQ0FBR2pCLFdBQVU7O2tEQUNaLDhEQUFDa0I7a0RBQUc7Ozs7OztrREFDSiw4REFBQ0E7a0RBQUc7Ozs7OztrREFHSiw4REFBQ0E7a0RBQUc7Ozs7OztrREFDSiw4REFBQ0E7a0RBQUc7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQVFoQjtHQXJpQndCdEY7S0FBQUEiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vc3JjL2NvbXBvbmVudHMvZG9tYWlucy9Nb2Rlcm5EbnNNYW5hZ2VyLmpzeD9kZDQ1Il0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIGNsaWVudFwiO1xyXG5pbXBvcnQgeyB1c2VTdGF0ZSwgdXNlRWZmZWN0IH0gZnJvbSBcInJlYWN0XCI7XHJcbmltcG9ydCB7XHJcbiAgVHlwb2dyYXBoeSxcclxuICBDYXJkLFxyXG4gIENhcmRCb2R5LFxyXG4gIEJ1dHRvbixcclxuICBJbnB1dCxcclxuICBTZWxlY3QsXHJcbiAgT3B0aW9uLFxyXG4gIEFsZXJ0LFxyXG4gIENoaXAsXHJcbiAgSWNvbkJ1dHRvbixcclxuICBUb29sdGlwLFxyXG59IGZyb20gXCJAbWF0ZXJpYWwtdGFpbHdpbmQvcmVhY3RcIjtcclxuaW1wb3J0IHtcclxuICBQbHVzLFxyXG4gIEdsb2JlLFxyXG4gIFNlcnZlcixcclxuICBaYXAsXHJcbiAgQ2hlY2tDaXJjbGUsXHJcbiAgQWxlcnRDaXJjbGUsXHJcbiAgQ29weSxcclxuICBFZGl0LFxyXG4gIFRyYXNoMixcclxuICBQb3dlcixcclxuICBXaWZpLFxyXG4gIFNoaWVsZCxcclxuICBBY3Rpdml0eSxcclxufSBmcm9tIFwibHVjaWRlLXJlYWN0XCI7XHJcbmltcG9ydCBkb21haW5NbmdTZXJ2aWNlIGZyb20gXCJAL2FwcC9zZXJ2aWNlcy9kb21haW5NbmdTZXJ2aWNlXCI7XHJcbmltcG9ydCB7IHRvYXN0LCBUb2FzdENvbnRhaW5lciB9IGZyb20gXCJyZWFjdC10b2FzdGlmeVwiO1xyXG5pbXBvcnQgXCJyZWFjdC10b2FzdGlmeS9kaXN0L1JlYWN0VG9hc3RpZnkuY3NzXCI7XHJcblxyXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBNb2Rlcm5EbnNNYW5hZ2VyKHsgZG9tYWluLCBvblVwZGF0ZSB9KSB7XHJcbiAgY29uc3QgW2Ruc1NlcnZpY2VBY3RpdmUsIHNldERuc1NlcnZpY2VBY3RpdmVdID0gdXNlU3RhdGUoZmFsc2UpO1xyXG4gIGNvbnN0IFtkbnNSZWNvcmRzLCBzZXREbnNSZWNvcmRzXSA9IHVzZVN0YXRlKFtdKTtcclxuICBjb25zdCBbbG9hZGluZywgc2V0TG9hZGluZ10gPSB1c2VTdGF0ZShmYWxzZSk7XHJcbiAgY29uc3QgW2FjdGl2YXRpbmdTZXJ2aWNlLCBzZXRBY3RpdmF0aW5nU2VydmljZV0gPSB1c2VTdGF0ZShmYWxzZSk7XHJcbiAgY29uc3QgW3Nob3dBZGRGb3JtLCBzZXRTaG93QWRkRm9ybV0gPSB1c2VTdGF0ZShmYWxzZSk7XHJcbiAgY29uc3QgW3NlbGVjdGVkUmVjb3JkVHlwZSwgc2V0U2VsZWN0ZWRSZWNvcmRUeXBlXSA9IHVzZVN0YXRlKFwiQVwiKTtcclxuXHJcbiAgLy8gRm9ybSBzdGF0ZXMgZm9yIGFkZGluZyByZWNvcmRzXHJcbiAgY29uc3QgW2Zvcm1EYXRhLCBzZXRGb3JtRGF0YV0gPSB1c2VTdGF0ZSh7XHJcbiAgICBuYW1lOiBcIkBcIixcclxuICAgIGNvbnRlbnQ6IFwiXCIsXHJcbiAgICB0dGw6IFwiMzYwMFwiLFxyXG4gIH0pO1xyXG5cclxuICAvLyBMb2FkIEROUyByZWNvcmRzXHJcbiAgY29uc3QgbG9hZERuc1JlY29yZHMgPSBhc3luYyAoKSA9PiB7XHJcbiAgICB0cnkge1xyXG4gICAgICBzZXRMb2FkaW5nKHRydWUpO1xyXG4gICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGRvbWFpbk1uZ1NlcnZpY2UuZ2V0RG5zUmVjb3Jkcyhkb21haW4uaWQsIHtcclxuICAgICAgICB0eXBlOiBzZWxlY3RlZFJlY29yZFR5cGUsXHJcbiAgICAgIH0pO1xyXG4gICAgICBpZiAocmVzcG9uc2UuZGF0YS5zdWNjZXNzKSB7XHJcbiAgICAgICAgc2V0RG5zUmVjb3JkcyhyZXNwb25zZS5kYXRhLnJlY29yZHMgfHwgW10pO1xyXG4gICAgICAgIHNldERuc1NlcnZpY2VBY3RpdmUodHJ1ZSk7IC8vIElmIHdlIGNhbiBnZXQgcmVjb3Jkcywgc2VydmljZSBpcyBhY3RpdmVcclxuICAgICAgfVxyXG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgICAgY29uc29sZS5lcnJvcihcIkVycm9yIGxvYWRpbmcgRE5TIHJlY29yZHM6XCIsIGVycm9yKTtcclxuICAgICAgLy8gSWYgd2UgY2FuJ3QgZ2V0IHJlY29yZHMsIHNlcnZpY2UgbWlnaHQgbm90IGJlIGFjdGl2YXRlZFxyXG4gICAgICBzZXREbnNTZXJ2aWNlQWN0aXZlKGZhbHNlKTtcclxuICAgIH0gZmluYWxseSB7XHJcbiAgICAgIHNldExvYWRpbmcoZmFsc2UpO1xyXG4gICAgfVxyXG4gIH07XHJcblxyXG4gIHVzZUVmZmVjdCgoKSA9PiB7XHJcbiAgICBpZiAoZG9tYWluPy5pZCkge1xyXG4gICAgICBsb2FkRG5zUmVjb3JkcygpO1xyXG4gICAgfVxyXG4gIH0sIFtkb21haW4/LmlkXSk7XHJcblxyXG4gIC8vIEFjdGl2YXRlIEROUyBTZXJ2aWNlXHJcbiAgY29uc3QgYWN0aXZhdGVEbnNTZXJ2aWNlID0gYXN5bmMgKCkgPT4ge1xyXG4gICAgY29uc29sZS5sb2coXCJBY3RpdmF0aW5nIEROUyBzZXJ2aWNlIGZvciBkb21haW46XCIsIGRvbWFpbik7XHJcblxyXG4gICAgLy8gVHJ5IHRvIGdldCB0aGUgb3JkZXIgSUQgZnJvbSB2YXJpb3VzIHBvc3NpYmxlIGZpZWxkc1xyXG4gICAgY29uc3Qgb3JkZXJJZFRvVXNlID0gZG9tYWluPy5kb21haW5PcmRlcklkIHx8IGRvbWFpbj8ub3JkZXJpZCB8fCBkb21haW4/Lm9yZGVySWQgfHwgZG9tYWluPy5pZDtcclxuXHJcbiAgICBpZiAoIW9yZGVySWRUb1VzZSkge1xyXG4gICAgICB0b2FzdC5lcnJvcihcIkRvbWFpbiBvcmRlciBJRCBub3QgZm91bmQuIENhbm5vdCBhY3RpdmF0ZSBETlMgc2VydmljZS5cIik7XHJcbiAgICAgIGNvbnNvbGUuZXJyb3IoXCJEb21haW4gb2JqZWN0OlwiLCBkb21haW4pO1xyXG4gICAgICByZXR1cm47XHJcbiAgICB9XHJcblxyXG4gICAgY29uc29sZS5sb2coXCJVc2luZyBvcmRlciBJRCBmb3IgRE5TIGFjdGl2YXRpb246XCIsIG9yZGVySWRUb1VzZSk7XHJcblxyXG4gICAgdHJ5IHtcclxuICAgICAgc2V0QWN0aXZhdGluZ1NlcnZpY2UodHJ1ZSk7XHJcbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZG9tYWluTW5nU2VydmljZS5hY3RpdmF0ZURuc1NlcnZpY2Uob3JkZXJJZFRvVXNlKTtcclxuXHJcbiAgICAgIGlmIChyZXNwb25zZS5kYXRhLnN1Y2Nlc3MpIHtcclxuICAgICAgICBzZXREbnNTZXJ2aWNlQWN0aXZlKHRydWUpO1xyXG4gICAgICAgIHRvYXN0LnN1Y2Nlc3MoXCJETlMgc2VydmljZSBhY3RpdmF0ZWQgc3VjY2Vzc2Z1bGx5IVwiKTtcclxuICAgICAgICBhd2FpdCBsb2FkRG5zUmVjb3JkcygpOyAvLyBSZWxvYWQgcmVjb3JkcyBhZnRlciBhY3RpdmF0aW9uXHJcbiAgICAgIH0gZWxzZSB7XHJcbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKFxyXG4gICAgICAgICAgcmVzcG9uc2UuZGF0YS5lcnJvciB8fCBcIkZhaWxlZCB0byBhY3RpdmF0ZSBETlMgc2VydmljZVwiXHJcbiAgICAgICAgKTtcclxuICAgICAgfVxyXG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgICAgY29uc29sZS5lcnJvcihcIkVycm9yIGFjdGl2YXRpbmcgRE5TIHNlcnZpY2U6XCIsIGVycm9yKTtcclxuICAgICAgdG9hc3QuZXJyb3IoXHJcbiAgICAgICAgXCJGYWlsZWQgdG8gYWN0aXZhdGUgRE5TIHNlcnZpY2U6IFwiICtcclxuICAgICAgICAgIChlcnJvci5yZXNwb25zZT8uZGF0YT8uZGV0YWlscyB8fCBlcnJvci5tZXNzYWdlKVxyXG4gICAgICApO1xyXG4gICAgfSBmaW5hbGx5IHtcclxuICAgICAgc2V0QWN0aXZhdGluZ1NlcnZpY2UoZmFsc2UpO1xyXG4gICAgfVxyXG4gIH07XHJcblxyXG4gIC8vIEFkZCBETlMgUmVjb3JkXHJcbiAgY29uc3QgYWRkRG5zUmVjb3JkID0gYXN5bmMgKCkgPT4ge1xyXG4gICAgaWYgKCFmb3JtRGF0YS5jb250ZW50LnRyaW0oKSkge1xyXG4gICAgICB0b2FzdC5lcnJvcihcIlBsZWFzZSBlbnRlciB0aGUgcmVjb3JkIGNvbnRlbnRcIik7XHJcbiAgICAgIHJldHVybjtcclxuICAgIH1cclxuXHJcbiAgICB0cnkge1xyXG4gICAgICBzZXRMb2FkaW5nKHRydWUpO1xyXG4gICAgICBjb25zdCByZWNvcmREYXRhID0ge1xyXG4gICAgICAgIHR5cGU6IHNlbGVjdGVkUmVjb3JkVHlwZSxcclxuICAgICAgICBuYW1lOiBmb3JtRGF0YS5uYW1lLFxyXG4gICAgICAgIGNvbnRlbnQ6IGZvcm1EYXRhLmNvbnRlbnQsXHJcbiAgICAgICAgdHRsOiBwYXJzZUludChmb3JtRGF0YS50dGwpLFxyXG4gICAgICB9O1xyXG5cclxuICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBkb21haW5NbmdTZXJ2aWNlLmFkZERuc1JlY29yZChcclxuICAgICAgICBkb21haW4uaWQsXHJcbiAgICAgICAgcmVjb3JkRGF0YVxyXG4gICAgICApO1xyXG5cclxuICAgICAgaWYgKHJlc3BvbnNlLmRhdGEuc3VjY2Vzcykge1xyXG4gICAgICAgIHRvYXN0LnN1Y2Nlc3MoYCR7c2VsZWN0ZWRSZWNvcmRUeXBlfSByZWNvcmQgYWRkZWQgc3VjY2Vzc2Z1bGx5IWApO1xyXG4gICAgICAgIHNldEZvcm1EYXRhKHsgbmFtZTogXCJAXCIsIGNvbnRlbnQ6IFwiXCIsIHR0bDogXCIzNjAwXCIgfSk7XHJcbiAgICAgICAgc2V0U2hvd0FkZEZvcm0oZmFsc2UpO1xyXG4gICAgICAgIGF3YWl0IGxvYWREbnNSZWNvcmRzKCk7IC8vIFJlbG9hZCByZWNvcmRzXHJcbiAgICAgIH0gZWxzZSB7XHJcbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKHJlc3BvbnNlLmRhdGEuZXJyb3IgfHwgXCJGYWlsZWQgdG8gYWRkIEROUyByZWNvcmRcIik7XHJcbiAgICAgIH1cclxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICAgIGNvbnNvbGUuZXJyb3IoXCJFcnJvciBhZGRpbmcgRE5TIHJlY29yZDpcIiwgZXJyb3IpO1xyXG4gICAgICB0b2FzdC5lcnJvcihcclxuICAgICAgICBcIkZhaWxlZCB0byBhZGQgRE5TIHJlY29yZDogXCIgK1xyXG4gICAgICAgICAgKGVycm9yLnJlc3BvbnNlPy5kYXRhPy5kZXRhaWxzIHx8IGVycm9yLm1lc3NhZ2UpXHJcbiAgICAgICk7XHJcbiAgICB9IGZpbmFsbHkge1xyXG4gICAgICBzZXRMb2FkaW5nKGZhbHNlKTtcclxuICAgIH1cclxuICB9O1xyXG5cclxuICAvLyBDb3B5IHRvIGNsaXBib2FyZFxyXG4gIGNvbnN0IGNvcHlUb0NsaXBib2FyZCA9ICh0ZXh0KSA9PiB7XHJcbiAgICBuYXZpZ2F0b3IuY2xpcGJvYXJkLndyaXRlVGV4dCh0ZXh0KTtcclxuICAgIHRvYXN0LnN1Y2Nlc3MoXCJDb3BpZWQgdG8gY2xpcGJvYXJkIVwiKTtcclxuICB9O1xyXG5cclxuICAvLyBHZXQgcmVjb3JkIHR5cGUgaWNvbiBhbmQgY29sb3JcclxuICBjb25zdCBnZXRSZWNvcmRUeXBlSW5mbyA9ICh0eXBlKSA9PiB7XHJcbiAgICBjb25zdCBpbmZvID0ge1xyXG4gICAgICBBOiB7XHJcbiAgICAgICAgaWNvbjogR2xvYmUsXHJcbiAgICAgICAgY29sb3I6IFwiYmx1ZVwiLFxyXG4gICAgICAgIGJnQ29sb3I6IFwiYmctYmx1ZS01MFwiLFxyXG4gICAgICAgIHRleHRDb2xvcjogXCJ0ZXh0LWJsdWUtNzAwXCIsXHJcbiAgICAgIH0sXHJcbiAgICAgIEFBQUE6IHtcclxuICAgICAgICBpY29uOiBHbG9iZSxcclxuICAgICAgICBjb2xvcjogXCJpbmRpZ29cIixcclxuICAgICAgICBiZ0NvbG9yOiBcImJnLWluZGlnby01MFwiLFxyXG4gICAgICAgIHRleHRDb2xvcjogXCJ0ZXh0LWluZGlnby03MDBcIixcclxuICAgICAgfSxcclxuICAgICAgQ05BTUU6IHtcclxuICAgICAgICBpY29uOiBTZXJ2ZXIsXHJcbiAgICAgICAgY29sb3I6IFwicHVycGxlXCIsXHJcbiAgICAgICAgYmdDb2xvcjogXCJiZy1wdXJwbGUtNTBcIixcclxuICAgICAgICB0ZXh0Q29sb3I6IFwidGV4dC1wdXJwbGUtNzAwXCIsXHJcbiAgICAgIH0sXHJcbiAgICAgIE1YOiB7XHJcbiAgICAgICAgaWNvbjogV2lmaSxcclxuICAgICAgICBjb2xvcjogXCJncmVlblwiLFxyXG4gICAgICAgIGJnQ29sb3I6IFwiYmctZ3JlZW4tNTBcIixcclxuICAgICAgICB0ZXh0Q29sb3I6IFwidGV4dC1ncmVlbi03MDBcIixcclxuICAgICAgfSxcclxuICAgICAgVFhUOiB7XHJcbiAgICAgICAgaWNvbjogU2hpZWxkLFxyXG4gICAgICAgIGNvbG9yOiBcIm9yYW5nZVwiLFxyXG4gICAgICAgIGJnQ29sb3I6IFwiYmctb3JhbmdlLTUwXCIsXHJcbiAgICAgICAgdGV4dENvbG9yOiBcInRleHQtb3JhbmdlLTcwMFwiLFxyXG4gICAgICB9LFxyXG4gICAgICBOUzoge1xyXG4gICAgICAgIGljb246IEFjdGl2aXR5LFxyXG4gICAgICAgIGNvbG9yOiBcImdyYXlcIixcclxuICAgICAgICBiZ0NvbG9yOiBcImJnLWdyYXktNTBcIixcclxuICAgICAgICB0ZXh0Q29sb3I6IFwidGV4dC1ncmF5LTcwMFwiLFxyXG4gICAgICB9LFxyXG4gICAgICBTUlY6IHtcclxuICAgICAgICBpY29uOiBTZXJ2ZXIsXHJcbiAgICAgICAgY29sb3I6IFwicGlua1wiLFxyXG4gICAgICAgIGJnQ29sb3I6IFwiYmctcGluay01MFwiLFxyXG4gICAgICAgIHRleHRDb2xvcjogXCJ0ZXh0LXBpbmstNzAwXCIsXHJcbiAgICAgIH0sXHJcbiAgICB9O1xyXG4gICAgcmV0dXJuIGluZm9bdHlwZV0gfHwgaW5mby5BO1xyXG4gIH07XHJcblxyXG4gIC8vIEZvcm1hdCByZWNvcmQgbmFtZSBmb3IgZGlzcGxheVxyXG4gIGNvbnN0IGZvcm1hdFJlY29yZE5hbWUgPSAobmFtZSkgPT4ge1xyXG4gICAgaWYgKG5hbWUgPT09IFwiQFwiIHx8IG5hbWUgPT09IFwiXCIpIHtcclxuICAgICAgcmV0dXJuIGBAICgke2RvbWFpbj8ubmFtZX0pYDtcclxuICAgIH1cclxuICAgIHJldHVybiBgJHtuYW1lfS4ke2RvbWFpbj8ubmFtZX1gO1xyXG4gIH07XHJcblxyXG4gIC8vIEF2YWlsYWJsZSByZWNvcmQgdHlwZXMgKG9ubHkgc2hvdyBpbXBsZW1lbnRlZCBvbmVzKVxyXG4gIGNvbnN0IGF2YWlsYWJsZVJlY29yZFR5cGVzID0gW1xyXG4gICAge1xyXG4gICAgICB2YWx1ZTogXCJBXCIsXHJcbiAgICAgIGxhYmVsOiBcIkEgUmVjb3JkXCIsXHJcbiAgICAgIGRlc2NyaXB0aW9uOiBcIklQdjQgQWRkcmVzc1wiLFxyXG4gICAgICBpbXBsZW1lbnRlZDogdHJ1ZSxcclxuICAgIH0sXHJcbiAgICB7XHJcbiAgICAgIHZhbHVlOiBcIkFBQUFcIixcclxuICAgICAgbGFiZWw6IFwiQUFBQSBSZWNvcmRcIixcclxuICAgICAgZGVzY3JpcHRpb246IFwiSVB2NiBBZGRyZXNzXCIsXHJcbiAgICAgIGltcGxlbWVudGVkOiB0cnVlLFxyXG4gICAgfSxcclxuICAgIHtcclxuICAgICAgdmFsdWU6IFwiQ05BTUVcIixcclxuICAgICAgbGFiZWw6IFwiQ05BTUUgUmVjb3JkXCIsXHJcbiAgICAgIGRlc2NyaXB0aW9uOiBcIkRvbWFpbiBBbGlhc1wiLFxyXG4gICAgICBpbXBsZW1lbnRlZDogZmFsc2UsXHJcbiAgICB9LFxyXG4gICAge1xyXG4gICAgICB2YWx1ZTogXCJNWFwiLFxyXG4gICAgICBsYWJlbDogXCJNWCBSZWNvcmRcIixcclxuICAgICAgZGVzY3JpcHRpb246IFwiTWFpbCBTZXJ2ZXJcIixcclxuICAgICAgaW1wbGVtZW50ZWQ6IGZhbHNlLFxyXG4gICAgfSxcclxuICAgIHtcclxuICAgICAgdmFsdWU6IFwiVFhUXCIsXHJcbiAgICAgIGxhYmVsOiBcIlRYVCBSZWNvcmRcIixcclxuICAgICAgZGVzY3JpcHRpb246IFwiVGV4dCBEYXRhXCIsXHJcbiAgICAgIGltcGxlbWVudGVkOiBmYWxzZSxcclxuICAgIH0sXHJcbiAgICB7XHJcbiAgICAgIHZhbHVlOiBcIk5TXCIsXHJcbiAgICAgIGxhYmVsOiBcIk5TIFJlY29yZFwiLFxyXG4gICAgICBkZXNjcmlwdGlvbjogXCJOYW1lIFNlcnZlclwiLFxyXG4gICAgICBpbXBsZW1lbnRlZDogZmFsc2UsXHJcbiAgICB9LFxyXG4gICAge1xyXG4gICAgICB2YWx1ZTogXCJTUlZcIixcclxuICAgICAgbGFiZWw6IFwiU1JWIFJlY29yZFwiLFxyXG4gICAgICBkZXNjcmlwdGlvbjogXCJTZXJ2aWNlIExvY2F0aW9uXCIsXHJcbiAgICAgIGltcGxlbWVudGVkOiBmYWxzZSxcclxuICAgIH0sXHJcbiAgXTtcclxuXHJcbiAgcmV0dXJuIChcclxuICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS02XCI+XHJcbiAgICAgIHsvKiBETlMgU2VydmljZSBTdGF0dXMgKi99XHJcbiAgICAgIDxDYXJkIGNsYXNzTmFtZT1cImJvcmRlci1sLTQgYm9yZGVyLWwtYmx1ZS01MDBcIj5cclxuICAgICAgICA8Q2FyZEJvZHkgY2xhc3NOYW1lPVwicC02XCI+XHJcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlblwiPlxyXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGdhcC00XCI+XHJcbiAgICAgICAgICAgICAgPGRpdlxyXG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgcC0zIHJvdW5kZWQtZnVsbCAke1xyXG4gICAgICAgICAgICAgICAgICBkbnNTZXJ2aWNlQWN0aXZlID8gXCJiZy1ncmVlbi0xMDBcIiA6IFwiYmctZ3JheS0xMDBcIlxyXG4gICAgICAgICAgICAgICAgfWB9XHJcbiAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgPFBvd2VyXHJcbiAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17YGgtNiB3LTYgJHtcclxuICAgICAgICAgICAgICAgICAgICBkbnNTZXJ2aWNlQWN0aXZlID8gXCJ0ZXh0LWdyZWVuLTYwMFwiIDogXCJ0ZXh0LWdyYXktNDAwXCJcclxuICAgICAgICAgICAgICAgICAgfWB9XHJcbiAgICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgIDxkaXY+XHJcbiAgICAgICAgICAgICAgICA8VHlwb2dyYXBoeSB2YXJpYW50PVwiaDVcIiBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktODAwIG1iLTFcIj5cclxuICAgICAgICAgICAgICAgICAgRE5TIFNlcnZpY2UgU3RhdHVzXHJcbiAgICAgICAgICAgICAgICA8L1R5cG9ncmFwaHk+XHJcbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGdhcC0yXCI+XHJcbiAgICAgICAgICAgICAgICAgIDxDaGlwXHJcbiAgICAgICAgICAgICAgICAgICAgdmFsdWU9e2Ruc1NlcnZpY2VBY3RpdmUgPyBcIkFjdGl2ZVwiIDogXCJJbmFjdGl2ZVwifVxyXG4gICAgICAgICAgICAgICAgICAgIGNvbG9yPXtkbnNTZXJ2aWNlQWN0aXZlID8gXCJncmVlblwiIDogXCJncmF5XCJ9XHJcbiAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidGV4dC14c1wiXHJcbiAgICAgICAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICAgICAgICAgIDxUeXBvZ3JhcGh5IGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ncmF5LTYwMFwiPlxyXG4gICAgICAgICAgICAgICAgICAgIHtkbnNTZXJ2aWNlQWN0aXZlXHJcbiAgICAgICAgICAgICAgICAgICAgICA/IFwiRE5TIHNlcnZpY2UgaXMgYWN0aXZlIGFuZCByZWFkeSB0byBtYW5hZ2UgcmVjb3Jkc1wiXHJcbiAgICAgICAgICAgICAgICAgICAgICA6IFwiRE5TIHNlcnZpY2UgbmVlZHMgdG8gYmUgYWN0aXZhdGVkIGJlZm9yZSBtYW5hZ2luZyByZWNvcmRzXCJ9XHJcbiAgICAgICAgICAgICAgICAgIDwvVHlwb2dyYXBoeT5cclxuICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICA8L2Rpdj5cclxuXHJcbiAgICAgICAgICAgIHshZG5zU2VydmljZUFjdGl2ZSAmJiAoXHJcbiAgICAgICAgICAgICAgPEJ1dHRvblxyXG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiYmctYmx1ZS02MDAgaG92ZXI6YmctYmx1ZS03MDAgZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTJcIlxyXG4gICAgICAgICAgICAgICAgb25DbGljaz17YWN0aXZhdGVEbnNTZXJ2aWNlfVxyXG4gICAgICAgICAgICAgICAgZGlzYWJsZWQ9e2FjdGl2YXRpbmdTZXJ2aWNlfVxyXG4gICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgIHthY3RpdmF0aW5nU2VydmljZSA/IChcclxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJhbmltYXRlLXNwaW4gcm91bmRlZC1mdWxsIGgtNCB3LTQgYm9yZGVyLWItMiBib3JkZXItd2hpdGVcIj48L2Rpdj5cclxuICAgICAgICAgICAgICAgICkgOiAoXHJcbiAgICAgICAgICAgICAgICAgIDxaYXAgY2xhc3NOYW1lPVwiaC00IHctNFwiIC8+XHJcbiAgICAgICAgICAgICAgICApfVxyXG4gICAgICAgICAgICAgICAgQWN0aXZhdGUgRE5TIFNlcnZpY2VcclxuICAgICAgICAgICAgICA8L0J1dHRvbj5cclxuICAgICAgICAgICAgKX1cclxuICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgIDwvQ2FyZEJvZHk+XHJcbiAgICAgIDwvQ2FyZD5cclxuXHJcbiAgICAgIHsvKiBETlMgUmVjb3JkcyBNYW5hZ2VtZW50ICovfVxyXG4gICAgICB7ZG5zU2VydmljZUFjdGl2ZSAmJiAoXHJcbiAgICAgICAgPD5cclxuICAgICAgICAgIHsvKiBRdWljayBBZGQgUmVjb3JkIFNlY3Rpb24gKi99XHJcbiAgICAgICAgICA8Q2FyZD5cclxuICAgICAgICAgICAgPENhcmRCb2R5IGNsYXNzTmFtZT1cInAtNlwiPlxyXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuIG1iLTZcIj5cclxuICAgICAgICAgICAgICAgIDxkaXY+XHJcbiAgICAgICAgICAgICAgICAgIDxUeXBvZ3JhcGh5IHZhcmlhbnQ9XCJoNVwiIGNsYXNzTmFtZT1cInRleHQtZ3JheS04MDAgbWItMVwiPlxyXG4gICAgICAgICAgICAgICAgICAgIEROUyBSZWNvcmRzXHJcbiAgICAgICAgICAgICAgICAgIDwvVHlwb2dyYXBoeT5cclxuICAgICAgICAgICAgICAgICAgPFR5cG9ncmFwaHkgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyYXktNjAwXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgTWFuYWdlIHlvdXIgZG9tYWluJ3MgRE5TIHJlY29yZHNcclxuICAgICAgICAgICAgICAgICAgPC9UeXBvZ3JhcGh5PlxyXG4gICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICA8QnV0dG9uXHJcbiAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImJnLWJsdWUtNjAwIGhvdmVyOmJnLWJsdWUtNzAwIGZsZXggaXRlbXMtY2VudGVyIGdhcC0yXCJcclxuICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gc2V0U2hvd0FkZEZvcm0oIXNob3dBZGRGb3JtKX1cclxuICAgICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgICAgPFBsdXMgY2xhc3NOYW1lPVwiaC00IHctNFwiIC8+XHJcbiAgICAgICAgICAgICAgICAgIEFkZCBSZWNvcmRcclxuICAgICAgICAgICAgICAgIDwvQnV0dG9uPlxyXG4gICAgICAgICAgICAgIDwvZGl2PlxyXG5cclxuICAgICAgICAgICAgICB7LyogQWRkIFJlY29yZCBGb3JtICovfVxyXG4gICAgICAgICAgICAgIHtzaG93QWRkRm9ybSAmJiAoXHJcbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLWdyYXktNTAgcC02IHJvdW5kZWQtbGcgbWItNlwiPlxyXG4gICAgICAgICAgICAgICAgICA8VHlwb2dyYXBoeSB2YXJpYW50PVwiaDZcIiBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktODAwIG1iLTRcIj5cclxuICAgICAgICAgICAgICAgICAgICBBZGQgTmV3IEROUyBSZWNvcmRcclxuICAgICAgICAgICAgICAgICAgPC9UeXBvZ3JhcGh5PlxyXG5cclxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGdyaWQtY29scy0xIG1kOmdyaWQtY29scy0yIGxnOmdyaWQtY29scy00IGdhcC00IG1iLTRcIj5cclxuICAgICAgICAgICAgICAgICAgICA8U2VsZWN0XHJcbiAgICAgICAgICAgICAgICAgICAgICBsYWJlbD1cIlJlY29yZCBUeXBlXCJcclxuICAgICAgICAgICAgICAgICAgICAgIHZhbHVlPXtzZWxlY3RlZFJlY29yZFR5cGV9XHJcbiAgICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KHZhbCkgPT4gc2V0U2VsZWN0ZWRSZWNvcmRUeXBlKHZhbCl9XHJcbiAgICAgICAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgICAgICAge2F2YWlsYWJsZVJlY29yZFR5cGVzLm1hcCgodHlwZSkgPT4gKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8T3B0aW9uXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAga2V5PXt0eXBlLnZhbHVlfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIHZhbHVlPXt0eXBlLnZhbHVlfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIGRpc2FibGVkPXshdHlwZS5pbXBsZW1lbnRlZH1cclxuICAgICAgICAgICAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuIHctZnVsbFwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4+e3R5cGUubGFiZWx9PC9zcGFuPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgeyF0eXBlLmltcGxlbWVudGVkICYmIChcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPENoaXBcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB2YWx1ZT1cIlNvb25cIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHNpemU9XCJzbVwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY29sb3I9XCJhbWJlclwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidGV4dC14c1wiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICApfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8L09wdGlvbj5cclxuICAgICAgICAgICAgICAgICAgICAgICkpfVxyXG4gICAgICAgICAgICAgICAgICAgIDwvU2VsZWN0PlxyXG5cclxuICAgICAgICAgICAgICAgICAgICA8SW5wdXRcclxuICAgICAgICAgICAgICAgICAgICAgIGxhYmVsPVwiTmFtZVwiXHJcbiAgICAgICAgICAgICAgICAgICAgICB2YWx1ZT17Zm9ybURhdGEubmFtZX1cclxuICAgICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT5cclxuICAgICAgICAgICAgICAgICAgICAgICAgc2V0Rm9ybURhdGEoKHByZXYpID0+ICh7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgLi4ucHJldixcclxuICAgICAgICAgICAgICAgICAgICAgICAgICBuYW1lOiBlLnRhcmdldC52YWx1ZSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgfSkpXHJcbiAgICAgICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIkAsIHd3dywgbWFpbCwgZXRjLlwiXHJcbiAgICAgICAgICAgICAgICAgICAgLz5cclxuXHJcbiAgICAgICAgICAgICAgICAgICAgPElucHV0XHJcbiAgICAgICAgICAgICAgICAgICAgICBsYWJlbD17XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHNlbGVjdGVkUmVjb3JkVHlwZSA9PT0gXCJBXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICA/IFwiSVB2NCBBZGRyZXNzXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICA6IFwiSVB2NiBBZGRyZXNzXCJcclxuICAgICAgICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgICAgICAgIHZhbHVlPXtmb3JtRGF0YS5jb250ZW50fVxyXG4gICAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICBzZXRGb3JtRGF0YSgocHJldikgPT4gKHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAuLi5wcmV2LFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIGNvbnRlbnQ6IGUudGFyZ2V0LnZhbHVlLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICB9KSlcclxuICAgICAgICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPXtcclxuICAgICAgICAgICAgICAgICAgICAgICAgc2VsZWN0ZWRSZWNvcmRUeXBlID09PSBcIkFcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgID8gXCIxOTIuMTY4LjEuMVwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgOiBcIjIwMDE6ZGI4OjoxXCJcclxuICAgICAgICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgICAgICAvPlxyXG5cclxuICAgICAgICAgICAgICAgICAgICA8U2VsZWN0XHJcbiAgICAgICAgICAgICAgICAgICAgICBsYWJlbD1cIlRUTFwiXHJcbiAgICAgICAgICAgICAgICAgICAgICB2YWx1ZT17Zm9ybURhdGEudHRsfVxyXG4gICAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyh2YWwpID0+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHNldEZvcm1EYXRhKChwcmV2KSA9PiAoeyAuLi5wcmV2LCB0dGw6IHZhbCB9KSlcclxuICAgICAgICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8T3B0aW9uIHZhbHVlPVwiMzAwXCI+NSBtaW51dGVzPC9PcHRpb24+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8T3B0aW9uIHZhbHVlPVwiMTgwMFwiPjMwIG1pbnV0ZXM8L09wdGlvbj5cclxuICAgICAgICAgICAgICAgICAgICAgIDxPcHRpb24gdmFsdWU9XCIzNjAwXCI+MSBob3VyPC9PcHRpb24+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8T3B0aW9uIHZhbHVlPVwiMTQ0MDBcIj40IGhvdXJzPC9PcHRpb24+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8T3B0aW9uIHZhbHVlPVwiODY0MDBcIj4xIGRheTwvT3B0aW9uPlxyXG4gICAgICAgICAgICAgICAgICAgIDwvU2VsZWN0PlxyXG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuXHJcbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBnYXAtM1wiPlxyXG4gICAgICAgICAgICAgICAgICAgIDxCdXR0b25cclxuICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImJnLWdyZWVuLTYwMCBob3ZlcjpiZy1ncmVlbi03MDAgZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTJcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17YWRkRG5zUmVjb3JkfVxyXG4gICAgICAgICAgICAgICAgICAgICAgZGlzYWJsZWQ9e2xvYWRpbmd9XHJcbiAgICAgICAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgICAgICAge2xvYWRpbmcgPyAoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYW5pbWF0ZS1zcGluIHJvdW5kZWQtZnVsbCBoLTQgdy00IGJvcmRlci1iLTIgYm9yZGVyLXdoaXRlXCI+PC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgICApIDogKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8Q2hlY2tDaXJjbGUgY2xhc3NOYW1lPVwiaC00IHctNFwiIC8+XHJcbiAgICAgICAgICAgICAgICAgICAgICApfVxyXG4gICAgICAgICAgICAgICAgICAgICAgQWRkIFJlY29yZFxyXG4gICAgICAgICAgICAgICAgICAgIDwvQnV0dG9uPlxyXG4gICAgICAgICAgICAgICAgICAgIDxCdXR0b25cclxuICAgICAgICAgICAgICAgICAgICAgIHZhcmlhbnQ9XCJvdXRsaW5lZFwiXHJcbiAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzZXRTaG93QWRkRm9ybShmYWxzZSl9XHJcbiAgICAgICAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgICAgICAgQ2FuY2VsXHJcbiAgICAgICAgICAgICAgICAgICAgPC9CdXR0b24+XHJcbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgKX1cclxuXHJcbiAgICAgICAgICAgICAgey8qIFJlY29yZHMgTGlzdCAqL31cclxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktM1wiPlxyXG4gICAgICAgICAgICAgICAge2xvYWRpbmcgJiYgZG5zUmVjb3Jkcy5sZW5ndGggPT09IDAgPyAoXHJcbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXIgcHktOFwiPlxyXG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYW5pbWF0ZS1zcGluIHJvdW5kZWQtZnVsbCBoLTggdy04IGJvcmRlci1iLTIgYm9yZGVyLWJsdWUtNjAwIG14LWF1dG8gbWItNFwiPjwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgIDxUeXBvZ3JhcGh5IGNsYXNzTmFtZT1cInRleHQtZ3JheS02MDBcIj5cclxuICAgICAgICAgICAgICAgICAgICAgIExvYWRpbmcgRE5TIHJlY29yZHMuLi5cclxuICAgICAgICAgICAgICAgICAgICA8L1R5cG9ncmFwaHk+XHJcbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgKSA6IGRuc1JlY29yZHMubGVuZ3RoID09PSAwID8gKFxyXG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyIHB5LTEyIGJnLWdyYXktNTAgcm91bmRlZC1sZ1wiPlxyXG4gICAgICAgICAgICAgICAgICAgIDxHbG9iZSBjbGFzc05hbWU9XCJoLTEyIHctMTIgdGV4dC1ncmF5LTQwMCBteC1hdXRvIG1iLTRcIiAvPlxyXG4gICAgICAgICAgICAgICAgICAgIDxUeXBvZ3JhcGh5IHZhcmlhbnQ9XCJoNlwiIGNsYXNzTmFtZT1cInRleHQtZ3JheS02MDAgbWItMlwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgTm8gRE5TIFJlY29yZHMgRm91bmRcclxuICAgICAgICAgICAgICAgICAgICA8L1R5cG9ncmFwaHk+XHJcbiAgICAgICAgICAgICAgICAgICAgPFR5cG9ncmFwaHkgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyYXktNTAwIG1iLTRcIj5cclxuICAgICAgICAgICAgICAgICAgICAgIFN0YXJ0IGJ5IGFkZGluZyB5b3VyIGZpcnN0IEROUyByZWNvcmQgdG8gY29uZmlndXJlIHlvdXJcclxuICAgICAgICAgICAgICAgICAgICAgIGRvbWFpbi5cclxuICAgICAgICAgICAgICAgICAgICA8L1R5cG9ncmFwaHk+XHJcbiAgICAgICAgICAgICAgICAgICAgPEJ1dHRvblxyXG4gICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiYmctYmx1ZS02MDAgaG92ZXI6YmctYmx1ZS03MDBcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gc2V0U2hvd0FkZEZvcm0odHJ1ZSl9XHJcbiAgICAgICAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgICAgICAgQWRkIFlvdXIgRmlyc3QgUmVjb3JkXHJcbiAgICAgICAgICAgICAgICAgICAgPC9CdXR0b24+XHJcbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgKSA6IChcclxuICAgICAgICAgICAgICAgICAgZG5zUmVjb3Jkcy5tYXAoKHJlY29yZCkgPT4ge1xyXG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IHR5cGVJbmZvID0gZ2V0UmVjb3JkVHlwZUluZm8ocmVjb3JkLnR5cGUpO1xyXG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IEljb25Db21wb25lbnQgPSB0eXBlSW5mby5pY29uO1xyXG5cclxuICAgICAgICAgICAgICAgICAgICByZXR1cm4gKFxyXG4gICAgICAgICAgICAgICAgICAgICAgPENhcmRcclxuICAgICAgICAgICAgICAgICAgICAgICAga2V5PXtyZWNvcmQuaWR9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImJvcmRlciBib3JkZXItZ3JheS0yMDAgaG92ZXI6c2hhZG93LW1kIHRyYW5zaXRpb24tc2hhZG93XCJcclxuICAgICAgICAgICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPENhcmRCb2R5IGNsYXNzTmFtZT1cInAtNFwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGdhcC00IGZsZXgtMVwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgcC0yIHJvdW5kZWQtbGcgJHt0eXBlSW5mby5iZ0NvbG9yfWB9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8SWNvbkNvbXBvbmVudFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgaC01IHctNSAke3R5cGVJbmZvLnRleHRDb2xvcn1gfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4LTFcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGdhcC0zIG1iLTFcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxDaGlwXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHZhbHVlPXtyZWNvcmQudHlwZX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY29sb3I9e3R5cGVJbmZvLmNvbG9yfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ0ZXh0LXhzXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8VHlwb2dyYXBoeSBjbGFzc05hbWU9XCJmb250LW1lZGl1bSB0ZXh0LWdyYXktOTAwXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtmb3JtYXRSZWNvcmROYW1lKHJlY29yZC5uYW1lKX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvVHlwb2dyYXBoeT5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxUeXBvZ3JhcGh5IGNsYXNzTmFtZT1cInRleHQteHMgdGV4dC1ncmF5LTUwMFwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBUVEw6IHtyZWNvcmQudHRsfXNcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvVHlwb2dyYXBoeT5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMlwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPFR5cG9ncmFwaHkgY2xhc3NOYW1lPVwiZm9udC1tb25vIHRleHQtc20gdGV4dC1ncmF5LTcwMFwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7cmVjb3JkLmNvbnRlbnR9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L1R5cG9ncmFwaHk+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8VG9vbHRpcCBjb250ZW50PVwiQ29weSB0byBjbGlwYm9hcmRcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPEljb25CdXR0b25cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB2YXJpYW50PVwidGV4dFwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgc2l6ZT1cInNtXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY29weVRvQ2xpcGJvYXJkKHJlY29yZC5jb250ZW50KVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJwLTFcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPENvcHkgY2xhc3NOYW1lPVwiaC0zIHctM1wiIC8+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvSWNvbkJ1dHRvbj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvVG9vbHRpcD5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGdhcC0yXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxUb29sdGlwIGNvbnRlbnQ9XCJFZGl0IHJlY29yZFwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxJY29uQnV0dG9uXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB2YXJpYW50PVwidGV4dFwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBzaXplPVwic21cIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidGV4dC1ibHVlLTYwMCBob3ZlcjpiZy1ibHVlLTUwXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGRpc2FibGVkXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPEVkaXQgY2xhc3NOYW1lPVwiaC00IHctNFwiIC8+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9JY29uQnV0dG9uPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L1Rvb2x0aXA+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxUb29sdGlwIGNvbnRlbnQ9XCJEZWxldGUgcmVjb3JkXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPEljb25CdXR0b25cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHZhcmlhbnQ9XCJ0ZXh0XCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHNpemU9XCJzbVwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ0ZXh0LXJlZC02MDAgaG92ZXI6YmctcmVkLTUwXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGRpc2FibGVkXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPFRyYXNoMiBjbGFzc05hbWU9XCJoLTQgdy00XCIgLz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L0ljb25CdXR0b24+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvVG9vbHRpcD5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8L0NhcmRCb2R5PlxyXG4gICAgICAgICAgICAgICAgICAgICAgPC9DYXJkPlxyXG4gICAgICAgICAgICAgICAgICAgICk7XHJcbiAgICAgICAgICAgICAgICAgIH0pXHJcbiAgICAgICAgICAgICAgICApfVxyXG4gICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICA8L0NhcmRCb2R5PlxyXG4gICAgICAgICAgPC9DYXJkPlxyXG4gICAgICAgIDwvPlxyXG4gICAgICApfVxyXG5cclxuICAgICAgey8qIEluZm9ybWF0aW9uIEFsZXJ0ICovfVxyXG4gICAgICA8QWxlcnQgY29sb3I9XCJibHVlXCIgY2xhc3NOYW1lPVwicHktM1wiPlxyXG4gICAgICAgIDxBbGVydENpcmNsZSBjbGFzc05hbWU9XCJoLTQgdy00XCIgLz5cclxuICAgICAgICA8ZGl2PlxyXG4gICAgICAgICAgPFR5cG9ncmFwaHkgY2xhc3NOYW1lPVwiZm9udC1tZWRpdW0gbWItMVwiPlxyXG4gICAgICAgICAgICBETlMgTWFuYWdlbWVudCBJbmZvcm1hdGlvblxyXG4gICAgICAgICAgPC9UeXBvZ3JhcGh5PlxyXG4gICAgICAgICAgPHVsIGNsYXNzTmFtZT1cInRleHQtc20gc3BhY2UteS0xXCI+XHJcbiAgICAgICAgICAgIDxsaT7igKIgQ3VycmVudGx5IHN1cHBvcnRpbmcgQSBhbmQgQUFBQSByZWNvcmQgdHlwZXM8L2xpPlxyXG4gICAgICAgICAgICA8bGk+XHJcbiAgICAgICAgICAgICAg4oCiIEFkZGl0aW9uYWwgcmVjb3JkIHR5cGVzIChDTkFNRSwgTVgsIFRYVCwgTlMsIFNSVikgY29taW5nIHNvb25cclxuICAgICAgICAgICAgPC9saT5cclxuICAgICAgICAgICAgPGxpPuKAoiBETlMgY2hhbmdlcyBtYXkgdGFrZSAyNC00OCBob3VycyB0byBwcm9wYWdhdGUgZ2xvYmFsbHk8L2xpPlxyXG4gICAgICAgICAgICA8bGk+XHJcbiAgICAgICAgICAgICAg4oCiIEFsd2F5cyBiYWNrdXAgeW91ciBETlMgY29uZmlndXJhdGlvbiBiZWZvcmUgbWFraW5nIGNoYW5nZXNcclxuICAgICAgICAgICAgPC9saT5cclxuICAgICAgICAgIDwvdWw+XHJcbiAgICAgICAgPC9kaXY+XHJcbiAgICAgIDwvQWxlcnQ+XHJcbiAgICA8L2Rpdj5cclxuICApO1xyXG59XHJcbiJdLCJuYW1lcyI6WyJ1c2VTdGF0ZSIsInVzZUVmZmVjdCIsIlR5cG9ncmFwaHkiLCJDYXJkIiwiQ2FyZEJvZHkiLCJCdXR0b24iLCJJbnB1dCIsIlNlbGVjdCIsIk9wdGlvbiIsIkFsZXJ0IiwiQ2hpcCIsIkljb25CdXR0b24iLCJUb29sdGlwIiwiUGx1cyIsIkdsb2JlIiwiU2VydmVyIiwiWmFwIiwiQ2hlY2tDaXJjbGUiLCJBbGVydENpcmNsZSIsIkNvcHkiLCJFZGl0IiwiVHJhc2gyIiwiUG93ZXIiLCJXaWZpIiwiU2hpZWxkIiwiQWN0aXZpdHkiLCJkb21haW5NbmdTZXJ2aWNlIiwidG9hc3QiLCJUb2FzdENvbnRhaW5lciIsIk1vZGVybkRuc01hbmFnZXIiLCJkb21haW4iLCJvblVwZGF0ZSIsImRuc1NlcnZpY2VBY3RpdmUiLCJzZXREbnNTZXJ2aWNlQWN0aXZlIiwiZG5zUmVjb3JkcyIsInNldERuc1JlY29yZHMiLCJsb2FkaW5nIiwic2V0TG9hZGluZyIsImFjdGl2YXRpbmdTZXJ2aWNlIiwic2V0QWN0aXZhdGluZ1NlcnZpY2UiLCJzaG93QWRkRm9ybSIsInNldFNob3dBZGRGb3JtIiwic2VsZWN0ZWRSZWNvcmRUeXBlIiwic2V0U2VsZWN0ZWRSZWNvcmRUeXBlIiwiZm9ybURhdGEiLCJzZXRGb3JtRGF0YSIsIm5hbWUiLCJjb250ZW50IiwidHRsIiwibG9hZERuc1JlY29yZHMiLCJyZXNwb25zZSIsImdldERuc1JlY29yZHMiLCJpZCIsInR5cGUiLCJkYXRhIiwic3VjY2VzcyIsInJlY29yZHMiLCJlcnJvciIsImNvbnNvbGUiLCJhY3RpdmF0ZURuc1NlcnZpY2UiLCJsb2ciLCJvcmRlcklkVG9Vc2UiLCJkb21haW5PcmRlcklkIiwib3JkZXJpZCIsIm9yZGVySWQiLCJFcnJvciIsImRldGFpbHMiLCJtZXNzYWdlIiwiYWRkRG5zUmVjb3JkIiwidHJpbSIsInJlY29yZERhdGEiLCJwYXJzZUludCIsImNvcHlUb0NsaXBib2FyZCIsInRleHQiLCJuYXZpZ2F0b3IiLCJjbGlwYm9hcmQiLCJ3cml0ZVRleHQiLCJnZXRSZWNvcmRUeXBlSW5mbyIsImluZm8iLCJBIiwiaWNvbiIsImNvbG9yIiwiYmdDb2xvciIsInRleHRDb2xvciIsIkFBQUEiLCJDTkFNRSIsIk1YIiwiVFhUIiwiTlMiLCJTUlYiLCJmb3JtYXRSZWNvcmROYW1lIiwiYXZhaWxhYmxlUmVjb3JkVHlwZXMiLCJ2YWx1ZSIsImxhYmVsIiwiZGVzY3JpcHRpb24iLCJpbXBsZW1lbnRlZCIsImRpdiIsImNsYXNzTmFtZSIsInZhcmlhbnQiLCJvbkNsaWNrIiwiZGlzYWJsZWQiLCJvbkNoYW5nZSIsInZhbCIsIm1hcCIsInNwYW4iLCJzaXplIiwiZSIsInByZXYiLCJ0YXJnZXQiLCJwbGFjZWhvbGRlciIsImxlbmd0aCIsInJlY29yZCIsInR5cGVJbmZvIiwiSWNvbkNvbXBvbmVudCIsInVsIiwibGkiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/domains/ModernDnsManager.jsx\n"));

/***/ })

});