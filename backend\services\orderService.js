const Order = require("../models/Order");
const SubOrder = require("../models/SubOrder");
const Cart = require("../models/Cart");
const OrderStatus = require("../constants/enums/order-status");
const { processPayment } = require("../controllers/paymentController");
const {
  sendOrderSummaryEmail,
  sendNewOrderAdminAlert,
} = require("../routes/sendEmail/sendEmail");
const domainRegistrationService = require("./dn-management/domainRegistrationService");

// ✅ Mark Order as Paid (Process Successful Payment)
exports.markOrderAsPaid = async (oid, transactionId) => {
  try {
    // console.log("🚀 Processing Successful Payment");

    const order = await Order.findByIdAndUpdate(
      oid,
      {
        status: OrderStatus.PROCESSING,
        datePaid: new Date(),
        isPaid: true,
        transactionId,
        isPaymentProcessed: true,
      },
      { new: true }
    )
      .populate("user")
      .populate({
        path: "subOrders",
        populate: { path: "package", model: "Package" },
      });

    await SubOrder.updateMany(
      { _id: { $in: order.subOrders } },
      { status: OrderStatus.PROCESSING }
    );

    await processPayment({
      userId: order.user._id,
      orderId: oid,
      paymentMethod: order.paymentMethod,
      status: OrderStatus.COMPLETED,
      transactionId,
    });

    // Process domain registrations if any
    try {
      console.log("🔄 Checking for domain registrations in order:", oid);
      const domainResults =
        await domainRegistrationService.processDomainRegistrations(order);

      if (domainResults && domainResults.length > 0) {
        console.log(
          `✅ Processed ${domainResults.length} domain registrations:`,
          domainResults
            .map((r) => `${r.domainName}: ${r.success ? "Success" : "Failed"}`)
            .join(", ")
        );
      } else {
        console.log("ℹ️ No domains to register in this order");
      }
    } catch (domainError) {
      // Log the error but don't fail the entire order processing
      console.error("⚠️ Error processing domain registrations:", domainError);
    }

    await sendOrderSummaryEmail(order);
    await sendNewOrderAdminAlert(order);
    await Cart.findOneAndDelete({ user: order.user._id });

    console.log("✅ Order processed successfully:", order);
    return true;
  } catch (error) {
    console.error("🚨 Error in markOrderAsPaid:", error);
    return false;
  }
};

// ❌ Mark Order as Failed (Process Failed Payment)
exports.markOrderAsFailed = async (oid, transactionId) => {
  try {
    console.log("🚨 Processing Failed Payment");

    const order = await Order.findByIdAndUpdate(
      oid,
      { status: OrderStatus.FAILED, transactionId, isPaymentProcessed: true },
      { new: true }
    );

    await SubOrder.updateMany(
      { _id: { $in: order.subOrders } },
      { status: OrderStatus.FAILED }
    );

    console.log("❌ Order marked as failed:", order);
  } catch (error) {
    console.error("🚨 Error in markOrderAsFailed:", error);
  }
};

// ❌ Mark Order as Pending (Process Failed Payment)
exports.markOrderAsPending = async (oid, transactionId) => {
  try {
    console.log("🚨 Processing Pending Payment");

    const order = await Order.findByIdAndUpdate(
      oid,
      { status: OrderStatus.PENDING, transactionId, isPaymentProcessed: true },
      { new: true }
    );

    await SubOrder.updateMany(
      { _id: { $in: order.subOrders } },
      { status: OrderStatus.PENDING }
    );

    console.log("❌ Order marked as PENDING:", order);
  } catch (error) {
    console.error("🚨 Error in markOrderAsPENDING:", error);
  }
};
