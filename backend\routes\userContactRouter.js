const express = require("express");
const {
  getUserDomain<PERSON>ontacts,
  createOrUpdateDomain<PERSON>ontact,
  updateDomain<PERSON>ontact,
  deleteDomainContact,
  copyDomainContact,
} = require("../controllers/dn-management/userContactController");
const { checkUserOrRefreshToken } = require("../midelwares/authorization");
const domainMngController = require("../controllers/dn-management/domainMngController");

const userContactRouter = express.Router();

// All routes require authentication
userContactRouter.use(checkUserOrRefreshToken);

// Get user's domain contacts
userContactRouter.get("/domain-contacts", getUserDomainContacts);

// Create or update a domain contact
userContactRouter.post("/domain-contacts", createOrUpdateDomainContact);

// Update an existing domain contact
userContactRouter.put("/domain-contacts/:contactType", updateDomainContact);

// Delete a domain contact
userContactRouter.delete("/domain-contacts/:contactType", deleteDomainContact);

// Copy contact from one type to another
userContactRouter.post("/domain-contacts/copy", copyDomainContact);

// Debug endpoint to test contact creation
userContactRouter.post("/debug-contact-creation", async (req, res) => {
  try {
    const { contactDetails } = req.body;
    const customerId = process.env.COMPANY_CUSTOMER_ID;

    if (!customerId) {
      return res.status(500).json({
        success: false,
        error:
          "Company customer ID not configured. Please contact administrator.",
      });
    }

    console.log("🧪 Debug: Testing contact creation");
    console.log("Contact details:", contactDetails);
    console.log("Customer ID:", customerId);

    const contactService = require("../services/contactService");

    // Test the contact service directly
    const result = await contactService.addContact(contactDetails, customerId);

    console.log("✅ Debug: Contact creation successful");
    console.log("Result:", result);

    // Extract contact ID to check if it's valid
    const contactId =
      result.contactId || result["contact-id"] || result.id || result;

    res.json({
      success: true,
      message: "Contact creation test successful",
      result,
      contactId,
      customerId,
      isValidContactId:
        contactId &&
        typeof contactId === "string" &&
        !contactId.startsWith("fallback-"),
    });
  } catch (error) {
    console.error("❌ Debug: Contact creation failed");
    console.error("Error:", error.message);
    console.error("Full error:", error);

    res.status(500).json({
      success: false,
      error: error.message,
      details: error.response?.data || error.stack,
    });
  }
});

// Sync all user contacts with external API (fix fallback contacts)
userContactRouter.post("/sync-contacts", async (req, res) => {
  try {
    const userId = req.user._id;
    const customerId = process.env.COMPANY_CUSTOMER_ID;

    if (!customerId) {
      return res.status(500).json({
        success: false,
        error:
          "Company customer ID not configured. Please contact administrator.",
      });
    }

    console.log("🔄 Syncing all contacts for user:", userId);

    const Contact = require("../models/Contact");
    const contactService = require("../services/contactService");

    // Find all contacts for this user
    const userContacts = await Contact.findByUser(userId);

    if (!userContacts || userContacts.length === 0) {
      return res.json({
        success: true,
        message: "No contacts found to sync",
        results: [],
      });
    }

    const syncResults = [];

    for (const contact of userContacts) {
      try {
        console.log(
          `🔄 Syncing ${contact.contactType} contact:`,
          contact.externalContactId
        );

        const contactData = contact.getApiData();
        const validatedData = contactService.validateContactData(contactData);

        // Use the sync function to handle both creation and updates
        const syncResult = await contactService.syncContact(
          validatedData,
          customerId,
          contact.externalContactId
        );

        // Update the contact in database if we got a new ID
        if (syncResult.contactId !== contact.externalContactId) {
          contact.externalContactId = syncResult.contactId;
          contact.customerId = customerId;
          contact.lastSyncedAt = new Date();
          await contact.save();
        }

        syncResults.push({
          contactType: contact.contactType,
          oldContactId: contact.externalContactId,
          newContactId: syncResult.contactId,
          action: syncResult.action,
          success: true,
        });

        console.log(`✅ Successfully synced ${contact.contactType} contact`);
      } catch (error) {
        console.error(
          `❌ Failed to sync ${contact.contactType} contact:`,
          error.message
        );

        syncResults.push({
          contactType: contact.contactType,
          contactId: contact.externalContactId,
          action: "failed",
          success: false,
          error: error.message,
        });
      }
    }

    const successCount = syncResults.filter((r) => r.success).length;
    const failureCount = syncResults.filter((r) => !r.success).length;

    res.json({
      success: true,
      message: `Contact sync completed. ${successCount} successful, ${failureCount} failed.`,
      results: syncResults,
      summary: {
        total: syncResults.length,
        successful: successCount,
        failed: failureCount,
      },
    });
  } catch (error) {
    console.error("❌ Contact sync failed:", error);
    res.status(500).json({
      success: false,
      error: "Failed to sync contacts",
      details: error.message,
    });
  }
});

// Route to activate DNS service
userContactRouter.post(
  "/activate-dns",
  domainMngController.activateDnsService
);

module.exports = userContactRouter;
