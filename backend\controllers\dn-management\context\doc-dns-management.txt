Activating the DNS Service
Description
Parameters
HTTP Method
Example Test URL Request
Response
Description
Activates the DNS service

Parameters
Name	Data Type	Required / Optional	Description
auth-userid	Integer	Required	Authentication Parameter
api-key	String	Required	Authentication Parameter
order-id	Integer	Required	Order Id of the Order for which the DNS service is to be activated
HTTP Method
POST

Example Test URL Request
https://test.httpapi.com/api/dns/activate.xml?auth-userid=0&api-key=key&order-id=0

Response
Returns a map containing status information.








Adding IPv4 Address Record
Description
Parameters
HTTP Method
Example Test URL Request
Response
Description
Adds an IPv4 Address (A) record.

Parameters
Name	Data Type	Required / Optional	Description
auth-userid	Integer	Required	Authentication Parameter
api-key	String	Required	Authentication Parameter
domain-name	String	Required	Domain name for which you want to add the A record
value	String	Required	An IPv4 address
host	String	Optional	
The host for which you need to add the A record. By default, IP address gets added for the domain name.

Example:

You may send this as "www", if u wish to add the A record as www.domainname.com.

ttl	Integer	Optional	Number of seconds the record needs to be cached by the DNS Resolvers. Default value is 14400.
HTTP Method
POST

Example Test URL Request


   https://test.httpapi.com/api/dns/manage/add-ipv4-record.json?auth-userid=0&api-key=key&domain-name=domain.asia&value=0.0.0.0


Response
Returns "Success" as the status of the response if the record is added successfully.

In case of any error, an "error" key with error description (as value) will be returned.







Adding IPv4 Address Record
Description
Parameters
HTTP Method
Example Test URL Request
Response
Description
Adds an IPv4 Address (A) record.

Parameters
Name	Data Type	Required / Optional	Description
auth-userid	Integer	Required	Authentication Parameter
api-key	String	Required	Authentication Parameter
domain-name	String	Required	Domain name for which you want to add the A record
value	String	Required	An IPv4 address
host	String	Optional	
The host for which you need to add the A record. By default, IP address gets added for the domain name.

Example:

You may send this as "www", if u wish to add the A record as www.domainname.com.

ttl	Integer	Optional	Number of seconds the record needs to be cached by the DNS Resolvers. Default value is 14400.
HTTP Method
POST

Example Test URL Request


   https://test.httpapi.com/api/dns/manage/add-ipv4-record.json?auth-userid=0&api-key=key&domain-name=domain.asia&value=0.0.0.0


Response
Returns "Success" as the status of the response if the record is added successfully.

In case of any error, an "error" key with error description (as value) will be returned.