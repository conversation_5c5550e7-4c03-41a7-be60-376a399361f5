DNS SERVICE API DOCUMENTATION
=============================


1. ACTIVATE DNS SERVICE
-----------------------

Description:
Activates the DNS service for a given order.

HTTP Method:
POST

Parameters:
-----------
Name         : auth-userid
Data Type    : Integer
Required     : Yes
Description  : Authentication Parameter

Name         : api-key
Data Type    : String
Required     : Yes
Description  : Authentication Parameter

Name         : order-id
Data Type    : Integer
Required     : Yes
Description  : Order ID of the order to activate DNS service

Example Test URL Request:
https://test.httpapi.com/api/dns/activate.xml?auth-userid=0&api-key=key&order-id=0

Response:
Returns a map containing status information.


2. ADD IPv4 ADDRESS RECORD (A RECORD)
-------------------------------------

Description:
Adds an IPv4 (A) record for a domain.

HTTP Method:
POST

Parameters:
-----------
Name         : auth-userid
Data Type    : Integer
Required     : Yes
Description  : Authentication Parameter

Name         : api-key
Data Type    : String
Required     : Yes
Description  : Authentication Parameter

Name         : domain-name
Data Type    : String
Required     : Yes
Description  : Domain name for which to add the A record

Name         : value
Data Type    : String
Required     : Yes
Description  : The IPv4 address to be added

Name         : host
Data Type    : String
Required     : No
Description  : Hostname (e.g., "www" for www.domain.com)

Name         : ttl
Data Type    : Integer
Required     : No
Description  : Time-to-live in seconds (default: 14400)

Example Test URL Request:
https://test.httpapi.com/api/dns/manage/add-ipv4-record.json?auth-userid=0&api-key=key&domain-name=domain.asia&value=0.0.0.0

Response:
Returns "Success" if the record is added successfully.
In case of error, returns "error" key with error description.


3. MODIFY IPv4 ADDRESS RECORD
-----------------------------

Description:
Modifies an existing IPv4 (A) record.

HTTP Method:
POST

Parameters:
-----------
Name         : auth-userid
Data Type    : Integer
Required     : Yes
Description  : Authentication Parameter

Name         : api-key
Data Type    : String
Required     : Yes
Description  : Authentication Parameter

Name         : domain-name
Data Type    : String
Required     : Yes
Description  : Domain name for which to modify the A record

Name         : host
Data Type    : String
Required     : No
Description  : Hostname (e.g., "www" for www.domain.com)

Name         : current-value
Data Type    : String
Required     : Yes
Description  : Current IPv4 address

Name         : new-value
Data Type    : String
Required     : Yes
Description  : New IPv4 address

Name         : ttl
Data Type    : Integer
Required     : No
Description  : Time-to-live in seconds (default: 14400)

Example Test URL Request:
https://test.httpapi.com/api/dns/manage/update-ipv4-record.json?auth-userid=0&api-key=key&domain-name=domain.asia&current-value=0.0.0.0&new-value=*******

Response:
Returns "Success" if the record is modified successfully.
In case of error, returns "ERROR" with an error message.


4. DELETE IPv4 ADDRESS RECORD
-----------------------------

Description:
Deletes an existing IPv4 (A) record.

HTTP Method:
POST

Parameters:
-----------
Name         : auth-userid
Data Type    : Integer
Required     : Yes
Description  : Authentication Parameter

Name         : api-key
Data Type    : String
Required     : Yes
Description  : Authentication Parameter

Name         : domain-name
Data Type    : String
Required     : Yes
Description  : Domain name for which to delete the A record

Name         : value
Data Type    : String
Required     : Yes
Description  : IPv4 address to delete

Name         : host
Data Type    : String
Required     : No
Description  : Hostname (e.g., "www" for www.domain.com)

Example Test URL Request:
https://test.httpapi.com/api/dns/manage/delete-ipv4-record.json?auth-userid=0&api-key=key&domain-name=domain.com&host=www&value=0.0.0.0

Response:
Returns "Success" if the record is deleted successfully or already does not exist.
In case of error, returns "error" key with error description.
