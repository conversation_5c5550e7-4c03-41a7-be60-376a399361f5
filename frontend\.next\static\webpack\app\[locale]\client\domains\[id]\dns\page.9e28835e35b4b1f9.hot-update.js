"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/client/domains/[id]/dns/page",{

/***/ "(app-pages-browser)/./src/components/domains/ModernDnsManager.jsx":
/*!*****************************************************!*\
  !*** ./src/components/domains/ModernDnsManager.jsx ***!
  \*****************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ ModernDnsManager; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @material-tailwind/react */ \"(app-pages-browser)/./node_modules/@material-tailwind/react/index.js\");\n/* harmony import */ var _material_tailwind_react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_CheckCircle_Copy_Edit_Globe_Plus_Power_Server_Shield_Trash2_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,CheckCircle,Copy,Edit,Globe,Plus,Power,Server,Shield,Trash2,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_CheckCircle_Copy_Edit_Globe_Plus_Power_Server_Shield_Trash2_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,CheckCircle,Copy,Edit,Globe,Plus,Power,Server,Shield,Trash2,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/server.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_CheckCircle_Copy_Edit_Globe_Plus_Power_Server_Shield_Trash2_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,CheckCircle,Copy,Edit,Globe,Plus,Power,Server,Shield,Trash2,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/wifi.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_CheckCircle_Copy_Edit_Globe_Plus_Power_Server_Shield_Trash2_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,CheckCircle,Copy,Edit,Globe,Plus,Power,Server,Shield,Trash2,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_CheckCircle_Copy_Edit_Globe_Plus_Power_Server_Shield_Trash2_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,CheckCircle,Copy,Edit,Globe,Plus,Power,Server,Shield,Trash2,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_CheckCircle_Copy_Edit_Globe_Plus_Power_Server_Shield_Trash2_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,CheckCircle,Copy,Edit,Globe,Plus,Power,Server,Shield,Trash2,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/power.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_CheckCircle_Copy_Edit_Globe_Plus_Power_Server_Shield_Trash2_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,CheckCircle,Copy,Edit,Globe,Plus,Power,Server,Shield,Trash2,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_CheckCircle_Copy_Edit_Globe_Plus_Power_Server_Shield_Trash2_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,CheckCircle,Copy,Edit,Globe,Plus,Power,Server,Shield,Trash2,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_CheckCircle_Copy_Edit_Globe_Plus_Power_Server_Shield_Trash2_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,CheckCircle,Copy,Edit,Globe,Plus,Power,Server,Shield,Trash2,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_CheckCircle_Copy_Edit_Globe_Plus_Power_Server_Shield_Trash2_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,CheckCircle,Copy,Edit,Globe,Plus,Power,Server,Shield,Trash2,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/copy.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_CheckCircle_Copy_Edit_Globe_Plus_Power_Server_Shield_Trash2_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,CheckCircle,Copy,Edit,Globe,Plus,Power,Server,Shield,Trash2,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_CheckCircle_Copy_Edit_Globe_Plus_Power_Server_Shield_Trash2_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,CheckCircle,Copy,Edit,Globe,Plus,Power,Server,Shield,Trash2,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_CheckCircle_Copy_Edit_Globe_Plus_Power_Server_Shield_Trash2_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,CheckCircle,Copy,Edit,Globe,Plus,Power,Server,Shield,Trash2,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _app_services_domainMngService__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/app/services/domainMngService */ \"(app-pages-browser)/./src/app/services/domainMngService.js\");\n/* harmony import */ var react_toastify__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-toastify */ \"(app-pages-browser)/./node_modules/react-toastify/dist/react-toastify.esm.mjs\");\n/* harmony import */ var react_toastify_dist_ReactToastify_css__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react-toastify/dist/ReactToastify.css */ \"(app-pages-browser)/./node_modules/react-toastify/dist/ReactToastify.css\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nfunction ModernDnsManager(param) {\n    let { domain, onUpdate } = param;\n    _s();\n    const [dnsServiceActive, setDnsServiceActive] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [dnsRecords, setDnsRecords] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [activatingService, setActivatingService] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showAddForm, setShowAddForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedRecordType, setSelectedRecordType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"A\");\n    // Form states for adding records\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: \"@\",\n        content: \"\",\n        ttl: \"3600\"\n    });\n    // Load DNS records\n    const loadDnsRecords = async ()=>{\n        try {\n            setLoading(true);\n            const response = await _app_services_domainMngService__WEBPACK_IMPORTED_MODULE_3__[\"default\"].getDnsRecords(domain.id, {\n                type: selectedRecordType\n            });\n            if (response.data.success) {\n                setDnsRecords(response.data.records || []);\n                setDnsServiceActive(true); // If we can get records, service is active\n            }\n        } catch (error) {\n            console.error(\"Error loading DNS records:\", error);\n            // If we can't get records, service might not be activated\n            setDnsServiceActive(false);\n        } finally{\n            setLoading(false);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (domain === null || domain === void 0 ? void 0 : domain.id) {\n            loadDnsRecords();\n        }\n    }, [\n        domain === null || domain === void 0 ? void 0 : domain.id\n    ]);\n    // Activate DNS Service\n    const activateDnsService = async ()=>{\n        console.log(\"Activating DNS service for domain:\", domain);\n        // Try to get the order ID from various possible fields\n        const orderIdToUse = (domain === null || domain === void 0 ? void 0 : domain.domainOrderId) || (domain === null || domain === void 0 ? void 0 : domain.orderid) || (domain === null || domain === void 0 ? void 0 : domain.orderId) || (domain === null || domain === void 0 ? void 0 : domain.id);\n        if (!orderIdToUse) {\n            react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.error(\"Domain order ID not found. Cannot activate DNS service.\");\n            console.error(\"Domain object:\", domain);\n            return;\n        }\n        console.log(\"Using order ID for DNS activation:\", orderIdToUse);\n        try {\n            setActivatingService(true);\n            console.log(\"\\uD83D\\uDD04 [DNS] Calling DNS activation API with order ID:\", orderIdToUse);\n            const response = await _app_services_domainMngService__WEBPACK_IMPORTED_MODULE_3__[\"default\"].activateDnsService(orderIdToUse);\n            console.log(\"✅ [DNS] DNS activation response received:\", response.data);\n            console.log(\"✅ [DNS] Response success flag:\", response.data.success);\n            console.log(\"✅ [DNS] Response activated flag:\", response.data.activated);\n            console.log(\"✅ [DNS] Raw API response:\", response.data.rawResponse);\n            if (response.data.success && response.data.activated) {\n                console.log(\"✅ [DNS] DNS service activated successfully, updating UI state\");\n                setDnsServiceActive(true);\n                react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.success(response.data.message || \"DNS service activated successfully!\");\n                // Reload DNS records after activation\n                console.log(\"\\uD83D\\uDD04 [DNS] Reloading DNS records after activation\");\n                await loadDnsRecords();\n                // Force a re-render by updating the domain state\n                if (typeof onUpdate === \"function\") {\n                    console.log(\"\\uD83D\\uDD04 [DNS] Updating domain state via onUpdate callback\");\n                    onUpdate({\n                        dnsServiceActive: true\n                    });\n                }\n            } else {\n                console.error(\"❌ [DNS] DNS activation failed:\", response.data);\n                throw new Error(response.data.error || response.data.message || \"Failed to activate DNS service\");\n            }\n        } catch (error) {\n            var _error_response, _error_response_data, _error_response1, _error_response_data1, _error_response2;\n            console.error(\"❌ [DNS] Error activating DNS service:\", error);\n            console.error(\"❌ [DNS] Error response:\", (_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.data);\n            const errorMessage = ((_error_response1 = error.response) === null || _error_response1 === void 0 ? void 0 : (_error_response_data = _error_response1.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.error) || ((_error_response2 = error.response) === null || _error_response2 === void 0 ? void 0 : (_error_response_data1 = _error_response2.data) === null || _error_response_data1 === void 0 ? void 0 : _error_response_data1.message) || error.message || \"Failed to activate DNS service\";\n            react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.error(\"Failed to activate DNS service: \" + errorMessage);\n        } finally{\n            setActivatingService(false);\n        }\n    };\n    // Add DNS Record\n    const addDnsRecord = async ()=>{\n        if (!formData.content.trim()) {\n            react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.error(\"Please enter the record content\");\n            return;\n        }\n        try {\n            setLoading(true);\n            const recordData = {\n                type: selectedRecordType,\n                name: formData.name,\n                content: formData.content,\n                ttl: parseInt(formData.ttl)\n            };\n            const response = await _app_services_domainMngService__WEBPACK_IMPORTED_MODULE_3__[\"default\"].addDnsRecord(domain.id, recordData);\n            if (response.data.success) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.success(\"\".concat(selectedRecordType, \" record added successfully!\"));\n                setFormData({\n                    name: \"@\",\n                    content: \"\",\n                    ttl: \"3600\"\n                });\n                setShowAddForm(false);\n                await loadDnsRecords(); // Reload records\n            } else {\n                throw new Error(response.data.error || \"Failed to add DNS record\");\n            }\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error(\"Error adding DNS record:\", error);\n            react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.error(\"Failed to add DNS record: \" + (((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.details) || error.message));\n        } finally{\n            setLoading(false);\n        }\n    };\n    // Copy to clipboard\n    const copyToClipboard = (text)=>{\n        navigator.clipboard.writeText(text);\n        react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.success(\"Copied to clipboard!\");\n    };\n    // Get record type icon and color\n    const getRecordTypeInfo = (type)=>{\n        const info = {\n            A: {\n                icon: _barrel_optimize_names_Activity_AlertCircle_CheckCircle_Copy_Edit_Globe_Plus_Power_Server_Shield_Trash2_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n                color: \"blue\",\n                bgColor: \"bg-blue-50\",\n                textColor: \"text-blue-700\"\n            },\n            AAAA: {\n                icon: _barrel_optimize_names_Activity_AlertCircle_CheckCircle_Copy_Edit_Globe_Plus_Power_Server_Shield_Trash2_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n                color: \"indigo\",\n                bgColor: \"bg-indigo-50\",\n                textColor: \"text-indigo-700\"\n            },\n            CNAME: {\n                icon: _barrel_optimize_names_Activity_AlertCircle_CheckCircle_Copy_Edit_Globe_Plus_Power_Server_Shield_Trash2_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n                color: \"purple\",\n                bgColor: \"bg-purple-50\",\n                textColor: \"text-purple-700\"\n            },\n            MX: {\n                icon: _barrel_optimize_names_Activity_AlertCircle_CheckCircle_Copy_Edit_Globe_Plus_Power_Server_Shield_Trash2_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n                color: \"green\",\n                bgColor: \"bg-green-50\",\n                textColor: \"text-green-700\"\n            },\n            TXT: {\n                icon: _barrel_optimize_names_Activity_AlertCircle_CheckCircle_Copy_Edit_Globe_Plus_Power_Server_Shield_Trash2_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n                color: \"orange\",\n                bgColor: \"bg-orange-50\",\n                textColor: \"text-orange-700\"\n            },\n            NS: {\n                icon: _barrel_optimize_names_Activity_AlertCircle_CheckCircle_Copy_Edit_Globe_Plus_Power_Server_Shield_Trash2_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n                color: \"gray\",\n                bgColor: \"bg-gray-50\",\n                textColor: \"text-gray-700\"\n            },\n            SRV: {\n                icon: _barrel_optimize_names_Activity_AlertCircle_CheckCircle_Copy_Edit_Globe_Plus_Power_Server_Shield_Trash2_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n                color: \"pink\",\n                bgColor: \"bg-pink-50\",\n                textColor: \"text-pink-700\"\n            }\n        };\n        return info[type] || info.A;\n    };\n    // Format record name for display\n    const formatRecordName = (name)=>{\n        if (name === \"@\" || name === \"\") {\n            return \"@ (\".concat(domain === null || domain === void 0 ? void 0 : domain.name, \")\");\n        }\n        return \"\".concat(name, \".\").concat(domain === null || domain === void 0 ? void 0 : domain.name);\n    };\n    // Available record types (only show implemented ones)\n    const availableRecordTypes = [\n        {\n            value: \"A\",\n            label: \"A Record\",\n            description: \"IPv4 Address\",\n            implemented: true\n        },\n        {\n            value: \"AAAA\",\n            label: \"AAAA Record\",\n            description: \"IPv6 Address\",\n            implemented: true\n        },\n        {\n            value: \"CNAME\",\n            label: \"CNAME Record\",\n            description: \"Domain Alias\",\n            implemented: false\n        },\n        {\n            value: \"MX\",\n            label: \"MX Record\",\n            description: \"Mail Server\",\n            implemented: false\n        },\n        {\n            value: \"TXT\",\n            label: \"TXT Record\",\n            description: \"Text Data\",\n            implemented: false\n        },\n        {\n            value: \"NS\",\n            label: \"NS Record\",\n            description: \"Name Server\",\n            implemented: false\n        },\n        {\n            value: \"SRV\",\n            label: \"SRV Record\",\n            description: \"Service Location\",\n            implemented: false\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                className: \"border-l-4 border-l-blue-500\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.CardBody, {\n                    className: \"p-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-3 rounded-full \".concat(dnsServiceActive ? \"bg-green-100\" : \"bg-gray-100\"),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_CheckCircle_Copy_Edit_Globe_Plus_Power_Server_Shield_Trash2_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            className: \"h-6 w-6 \".concat(dnsServiceActive ? \"text-green-600\" : \"text-gray-400\")\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                            lineNumber: 298,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                        lineNumber: 293,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Typography, {\n                                                variant: \"h5\",\n                                                className: \"text-gray-800 mb-1\",\n                                                children: \"DNS Service Status\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                                lineNumber: 305,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Chip, {\n                                                        value: dnsServiceActive ? \"Active\" : \"Inactive\",\n                                                        color: dnsServiceActive ? \"green\" : \"gray\",\n                                                        className: \"text-xs\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                                        lineNumber: 309,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Typography, {\n                                                        className: \"text-sm text-gray-600\",\n                                                        children: dnsServiceActive ? \"DNS service is active and ready to manage records\" : \"DNS service needs to be activated before managing records\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                                        lineNumber: 314,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                                lineNumber: 308,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                        lineNumber: 304,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                lineNumber: 292,\n                                columnNumber: 13\n                            }, this),\n                            !dnsServiceActive && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                className: \"bg-blue-600 hover:bg-blue-700 flex items-center gap-2\",\n                                onClick: activateDnsService,\n                                disabled: activatingService,\n                                children: [\n                                    activatingService ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-white\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                        lineNumber: 330,\n                                        columnNumber: 19\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_CheckCircle_Copy_Edit_Globe_Plus_Power_Server_Shield_Trash2_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                        lineNumber: 332,\n                                        columnNumber: 19\n                                    }, this),\n                                    \"Activate DNS Service\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                lineNumber: 324,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                        lineNumber: 291,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                    lineNumber: 290,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                lineNumber: 289,\n                columnNumber: 7\n            }, this),\n            dnsServiceActive && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.CardBody, {\n                        className: \"p-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Typography, {\n                                                variant: \"h5\",\n                                                className: \"text-gray-800 mb-1\",\n                                                children: \"DNS Records\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                                lineNumber: 349,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Typography, {\n                                                className: \"text-sm text-gray-600\",\n                                                children: \"Manage your domain's DNS records\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                                lineNumber: 352,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                        lineNumber: 348,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        className: \"bg-blue-600 hover:bg-blue-700 flex items-center gap-2\",\n                                        onClick: ()=>setShowAddForm(!showAddForm),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_CheckCircle_Copy_Edit_Globe_Plus_Power_Server_Shield_Trash2_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                className: \"h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                                lineNumber: 360,\n                                                columnNumber: 19\n                                            }, this),\n                                            \"Add Record\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                        lineNumber: 356,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                lineNumber: 347,\n                                columnNumber: 15\n                            }, this),\n                            showAddForm && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gray-50 p-6 rounded-lg mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Typography, {\n                                        variant: \"h6\",\n                                        className: \"text-gray-800 mb-4\",\n                                        children: \"Add New DNS Record\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                        lineNumber: 368,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Select, {\n                                                label: \"Record Type\",\n                                                value: selectedRecordType,\n                                                onChange: (val)=>setSelectedRecordType(val),\n                                                children: availableRecordTypes.map((type)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Option, {\n                                                        value: type.value,\n                                                        disabled: !type.implemented,\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between w-full\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: type.label\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                                                    lineNumber: 385,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                !type.implemented && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Chip, {\n                                                                    value: \"Soon\",\n                                                                    size: \"sm\",\n                                                                    color: \"amber\",\n                                                                    className: \"text-xs\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                                                    lineNumber: 387,\n                                                                    columnNumber: 31\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                                            lineNumber: 384,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, type.value, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                                        lineNumber: 379,\n                                                        columnNumber: 25\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                                lineNumber: 373,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Input, {\n                                                label: \"Name\",\n                                                value: formData.name,\n                                                onChange: (e)=>setFormData((prev)=>({\n                                                            ...prev,\n                                                            name: e.target.value\n                                                        })),\n                                                placeholder: \"@, www, mail, etc.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                                lineNumber: 399,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Input, {\n                                                label: selectedRecordType === \"A\" ? \"IPv4 Address\" : \"IPv6 Address\",\n                                                value: formData.content,\n                                                onChange: (e)=>setFormData((prev)=>({\n                                                            ...prev,\n                                                            content: e.target.value\n                                                        })),\n                                                placeholder: selectedRecordType === \"A\" ? \"***********\" : \"2001:db8::1\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                                lineNumber: 411,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Select, {\n                                                label: \"TTL\",\n                                                value: formData.ttl,\n                                                onChange: (val)=>setFormData((prev)=>({\n                                                            ...prev,\n                                                            ttl: val\n                                                        })),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Option, {\n                                                        value: \"300\",\n                                                        children: \"5 minutes\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                                        lineNumber: 438,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Option, {\n                                                        value: \"1800\",\n                                                        children: \"30 minutes\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                                        lineNumber: 439,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Option, {\n                                                        value: \"3600\",\n                                                        children: \"1 hour\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                                        lineNumber: 440,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Option, {\n                                                        value: \"14400\",\n                                                        children: \"4 hours\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                                        lineNumber: 441,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Option, {\n                                                        value: \"86400\",\n                                                        children: \"1 day\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                                        lineNumber: 442,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                                lineNumber: 431,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                        lineNumber: 372,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex gap-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                className: \"bg-green-600 hover:bg-green-700 flex items-center gap-2\",\n                                                onClick: addDnsRecord,\n                                                disabled: loading,\n                                                children: [\n                                                    loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-white\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                                        lineNumber: 453,\n                                                        columnNumber: 25\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_CheckCircle_Copy_Edit_Globe_Plus_Power_Server_Shield_Trash2_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                        className: \"h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                                        lineNumber: 455,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    \"Add Record\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                                lineNumber: 447,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                variant: \"outlined\",\n                                                onClick: ()=>setShowAddForm(false),\n                                                children: \"Cancel\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                                lineNumber: 459,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                        lineNumber: 446,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                lineNumber: 367,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-3\",\n                                children: loading && dnsRecords.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center py-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                            lineNumber: 473,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Typography, {\n                                            className: \"text-gray-600\",\n                                            children: \"Loading DNS records...\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                            lineNumber: 474,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                    lineNumber: 472,\n                                    columnNumber: 19\n                                }, this) : dnsRecords.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center py-12 bg-gray-50 rounded-lg\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_CheckCircle_Copy_Edit_Globe_Plus_Power_Server_Shield_Trash2_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                            className: \"h-12 w-12 text-gray-400 mx-auto mb-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                            lineNumber: 480,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Typography, {\n                                            variant: \"h6\",\n                                            className: \"text-gray-600 mb-2\",\n                                            children: \"No DNS Records Found\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                            lineNumber: 481,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Typography, {\n                                            className: \"text-sm text-gray-500 mb-4\",\n                                            children: \"Start by adding your first DNS record to configure your domain.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                            lineNumber: 484,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                            className: \"bg-blue-600 hover:bg-blue-700\",\n                                            onClick: ()=>setShowAddForm(true),\n                                            children: \"Add Your First Record\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                            lineNumber: 488,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                    lineNumber: 479,\n                                    columnNumber: 19\n                                }, this) : dnsRecords.map((record)=>{\n                                    const typeInfo = getRecordTypeInfo(record.type);\n                                    const IconComponent = typeInfo.icon;\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                        className: \"border border-gray-200 hover:shadow-md transition-shadow\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.CardBody, {\n                                            className: \"p-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-4 flex-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"p-2 rounded-lg \".concat(typeInfo.bgColor),\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(IconComponent, {\n                                                                    className: \"h-5 w-5 \".concat(typeInfo.textColor)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                                                    lineNumber: 511,\n                                                                    columnNumber: 33\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                                                lineNumber: 508,\n                                                                columnNumber: 31\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center gap-3 mb-1\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Chip, {\n                                                                                value: record.type,\n                                                                                color: typeInfo.color,\n                                                                                className: \"text-xs\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                                                                lineNumber: 518,\n                                                                                columnNumber: 35\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Typography, {\n                                                                                className: \"font-medium text-gray-900\",\n                                                                                children: formatRecordName(record.name)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                                                                lineNumber: 523,\n                                                                                columnNumber: 35\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Typography, {\n                                                                                className: \"text-xs text-gray-500\",\n                                                                                children: [\n                                                                                    \"TTL: \",\n                                                                                    record.ttl,\n                                                                                    \"s\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                                                                lineNumber: 526,\n                                                                                columnNumber: 35\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                                                        lineNumber: 517,\n                                                                        columnNumber: 33\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center gap-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Typography, {\n                                                                                className: \"font-mono text-sm text-gray-700\",\n                                                                                children: record.content\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                                                                lineNumber: 532,\n                                                                                columnNumber: 35\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Tooltip, {\n                                                                                content: \"Copy to clipboard\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.IconButton, {\n                                                                                    variant: \"text\",\n                                                                                    size: \"sm\",\n                                                                                    onClick: ()=>copyToClipboard(record.content),\n                                                                                    className: \"p-1\",\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_CheckCircle_Copy_Edit_Globe_Plus_Power_Server_Shield_Trash2_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                                        className: \"h-3 w-3\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                                                                        lineNumber: 544,\n                                                                                        columnNumber: 39\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                                                                    lineNumber: 536,\n                                                                                    columnNumber: 37\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                                                                lineNumber: 535,\n                                                                                columnNumber: 35\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                                                        lineNumber: 531,\n                                                                        columnNumber: 33\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                                                lineNumber: 516,\n                                                                columnNumber: 31\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                                        lineNumber: 507,\n                                                        columnNumber: 29\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Tooltip, {\n                                                                content: \"Edit record\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.IconButton, {\n                                                                    variant: \"text\",\n                                                                    size: \"sm\",\n                                                                    className: \"text-blue-600 hover:bg-blue-50\",\n                                                                    disabled: true,\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_CheckCircle_Copy_Edit_Globe_Plus_Power_Server_Shield_Trash2_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                        className: \"h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                                                        lineNumber: 559,\n                                                                        columnNumber: 35\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                                                    lineNumber: 553,\n                                                                    columnNumber: 33\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                                                lineNumber: 552,\n                                                                columnNumber: 31\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Tooltip, {\n                                                                content: \"Delete record\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.IconButton, {\n                                                                    variant: \"text\",\n                                                                    size: \"sm\",\n                                                                    className: \"text-red-600 hover:bg-red-50\",\n                                                                    disabled: true,\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_CheckCircle_Copy_Edit_Globe_Plus_Power_Server_Shield_Trash2_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                        className: \"h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                                                        lineNumber: 569,\n                                                                        columnNumber: 35\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                                                    lineNumber: 563,\n                                                                    columnNumber: 33\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                                                lineNumber: 562,\n                                                                columnNumber: 31\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                                        lineNumber: 551,\n                                                        columnNumber: 29\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                                lineNumber: 506,\n                                                columnNumber: 27\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                            lineNumber: 505,\n                                            columnNumber: 25\n                                        }, this)\n                                    }, record.id, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                        lineNumber: 501,\n                                        columnNumber: 23\n                                    }, this);\n                                })\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                lineNumber: 470,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                        lineNumber: 346,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                    lineNumber: 345,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Alert, {\n                color: \"blue\",\n                className: \"py-3\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_CheckCircle_Copy_Edit_Globe_Plus_Power_Server_Shield_Trash2_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                        className: \"h-4 w-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                        lineNumber: 587,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Typography, {\n                                className: \"font-medium mb-1\",\n                                children: \"DNS Management Information\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                lineNumber: 589,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                className: \"text-sm space-y-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: \"• Currently supporting A and AAAA record types\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                        lineNumber: 593,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: \"• Additional record types (CNAME, MX, TXT, NS, SRV) coming soon\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                        lineNumber: 594,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: \"• DNS changes may take 24-48 hours to propagate globally\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                        lineNumber: 597,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: \"• Always backup your DNS configuration before making changes\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                        lineNumber: 598,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                lineNumber: 592,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                        lineNumber: 588,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                lineNumber: 586,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n        lineNumber: 287,\n        columnNumber: 5\n    }, this);\n}\n_s(ModernDnsManager, \"/VsvIaj410JVfwrsR+Vaatw4hPY=\");\n_c = ModernDnsManager;\nvar _c;\n$RefreshReg$(_c, \"ModernDnsManager\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/domains/ModernDnsManager.jsx\n"));

/***/ })

});