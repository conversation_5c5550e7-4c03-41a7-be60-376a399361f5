"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/client/domains/[id]/dns/page",{

/***/ "(app-pages-browser)/./src/components/domains/ModernDnsManager.jsx":
/*!*****************************************************!*\
  !*** ./src/components/domains/ModernDnsManager.jsx ***!
  \*****************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ ModernDnsManager; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @material-tailwind/react */ \"(app-pages-browser)/./node_modules/@material-tailwind/react/index.js\");\n/* harmony import */ var _material_tailwind_react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_CheckCircle_Copy_Edit_Globe_Plus_Power_Server_Shield_Trash2_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,CheckCircle,Copy,Edit,Globe,Plus,Power,Server,Shield,Trash2,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_CheckCircle_Copy_Edit_Globe_Plus_Power_Server_Shield_Trash2_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,CheckCircle,Copy,Edit,Globe,Plus,Power,Server,Shield,Trash2,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/server.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_CheckCircle_Copy_Edit_Globe_Plus_Power_Server_Shield_Trash2_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,CheckCircle,Copy,Edit,Globe,Plus,Power,Server,Shield,Trash2,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/wifi.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_CheckCircle_Copy_Edit_Globe_Plus_Power_Server_Shield_Trash2_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,CheckCircle,Copy,Edit,Globe,Plus,Power,Server,Shield,Trash2,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_CheckCircle_Copy_Edit_Globe_Plus_Power_Server_Shield_Trash2_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,CheckCircle,Copy,Edit,Globe,Plus,Power,Server,Shield,Trash2,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_CheckCircle_Copy_Edit_Globe_Plus_Power_Server_Shield_Trash2_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,CheckCircle,Copy,Edit,Globe,Plus,Power,Server,Shield,Trash2,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/power.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_CheckCircle_Copy_Edit_Globe_Plus_Power_Server_Shield_Trash2_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,CheckCircle,Copy,Edit,Globe,Plus,Power,Server,Shield,Trash2,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_CheckCircle_Copy_Edit_Globe_Plus_Power_Server_Shield_Trash2_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,CheckCircle,Copy,Edit,Globe,Plus,Power,Server,Shield,Trash2,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_CheckCircle_Copy_Edit_Globe_Plus_Power_Server_Shield_Trash2_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,CheckCircle,Copy,Edit,Globe,Plus,Power,Server,Shield,Trash2,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_CheckCircle_Copy_Edit_Globe_Plus_Power_Server_Shield_Trash2_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,CheckCircle,Copy,Edit,Globe,Plus,Power,Server,Shield,Trash2,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/copy.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_CheckCircle_Copy_Edit_Globe_Plus_Power_Server_Shield_Trash2_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,CheckCircle,Copy,Edit,Globe,Plus,Power,Server,Shield,Trash2,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_CheckCircle_Copy_Edit_Globe_Plus_Power_Server_Shield_Trash2_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,CheckCircle,Copy,Edit,Globe,Plus,Power,Server,Shield,Trash2,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_CheckCircle_Copy_Edit_Globe_Plus_Power_Server_Shield_Trash2_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,CheckCircle,Copy,Edit,Globe,Plus,Power,Server,Shield,Trash2,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _app_services_domainMngService__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/app/services/domainMngService */ \"(app-pages-browser)/./src/app/services/domainMngService.js\");\n/* harmony import */ var react_toastify__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-toastify */ \"(app-pages-browser)/./node_modules/react-toastify/dist/react-toastify.esm.mjs\");\n/* harmony import */ var react_toastify_dist_ReactToastify_css__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react-toastify/dist/ReactToastify.css */ \"(app-pages-browser)/./node_modules/react-toastify/dist/ReactToastify.css\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nfunction ModernDnsManager(param) {\n    let { domain, onUpdate } = param;\n    _s();\n    const [dnsServiceActive, setDnsServiceActive] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [dnsRecords, setDnsRecords] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [activatingService, setActivatingService] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showAddForm, setShowAddForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedRecordType, setSelectedRecordType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"A\");\n    // Form states for adding records\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: \"@\",\n        content: \"\",\n        ttl: \"3600\"\n    });\n    // Load DNS records\n    const loadDnsRecords = async ()=>{\n        try {\n            setLoading(true);\n            const response = await _app_services_domainMngService__WEBPACK_IMPORTED_MODULE_3__[\"default\"].getDnsRecords(domain.id, {\n                type: selectedRecordType\n            });\n            if (response.data.success) {\n                setDnsRecords(response.data.records || []);\n                setDnsServiceActive(true); // If we can get records, service is active\n            }\n        } catch (error) {\n            console.error(\"Error loading DNS records:\", error);\n            // If we can't get records, service might not be activated\n            setDnsServiceActive(false);\n        } finally{\n            setLoading(false);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (domain === null || domain === void 0 ? void 0 : domain.id) {\n            loadDnsRecords();\n        }\n    }, [\n        domain === null || domain === void 0 ? void 0 : domain.id\n    ]);\n    // Update DNS service active state when domain changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (domain) {\n            console.log(\"\\uD83D\\uDD04 [DNS] Domain data updated:\", domain);\n            // Check if DNS service is already active\n            const isActive = domain.dnsServiceActive || domain.dnsActive || false;\n            console.log(\"\\uD83D\\uDD04 [DNS] Setting DNS service active state:\", isActive);\n            setDnsServiceActive(isActive);\n        }\n    }, [\n        domain\n    ]);\n    // Activate DNS Service\n    const activateDnsService = async ()=>{\n        console.log(\"Activating DNS service for domain:\", domain);\n        // Try to get the order ID from various possible fields\n        const orderIdToUse = (domain === null || domain === void 0 ? void 0 : domain.domainOrderId) || (domain === null || domain === void 0 ? void 0 : domain.orderid) || (domain === null || domain === void 0 ? void 0 : domain.orderId) || (domain === null || domain === void 0 ? void 0 : domain.id);\n        if (!orderIdToUse) {\n            react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.error(\"Domain order ID not found. Cannot activate DNS service.\");\n            console.error(\"Domain object:\", domain);\n            return;\n        }\n        console.log(\"Using order ID for DNS activation:\", orderIdToUse);\n        try {\n            setActivatingService(true);\n            console.log(\"\\uD83D\\uDD04 [DNS] Calling DNS activation API with order ID:\", orderIdToUse);\n            const response = await _app_services_domainMngService__WEBPACK_IMPORTED_MODULE_3__[\"default\"].activateDnsService(orderIdToUse);\n            console.log(\"✅ [DNS] DNS activation response received:\", response.data);\n            console.log(\"✅ [DNS] Response success flag:\", response.data.success);\n            console.log(\"✅ [DNS] Response activated flag:\", response.data.activated);\n            console.log(\"✅ [DNS] Raw API response:\", response.data.rawResponse);\n            if (response.data.success && response.data.activated) {\n                console.log(\"✅ [DNS] DNS service activated successfully, updating UI state\");\n                setDnsServiceActive(true);\n                react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.success(response.data.message || \"DNS service activated successfully!\");\n                // Reload DNS records after activation\n                console.log(\"\\uD83D\\uDD04 [DNS] Reloading DNS records after activation\");\n                await loadDnsRecords();\n                // Force a re-render by updating the domain state\n                if (typeof onUpdate === \"function\") {\n                    console.log(\"\\uD83D\\uDD04 [DNS] Updating domain state via onUpdate callback\");\n                    onUpdate({\n                        dnsServiceActive: true\n                    });\n                }\n            } else {\n                console.error(\"❌ [DNS] DNS activation failed:\", response.data);\n                throw new Error(response.data.error || response.data.message || \"Failed to activate DNS service\");\n            }\n        } catch (error) {\n            var _error_response, _error_response_data, _error_response1, _error_response_data1, _error_response2;\n            console.error(\"❌ [DNS] Error activating DNS service:\", error);\n            console.error(\"❌ [DNS] Error response:\", (_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.data);\n            const errorMessage = ((_error_response1 = error.response) === null || _error_response1 === void 0 ? void 0 : (_error_response_data = _error_response1.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.error) || ((_error_response2 = error.response) === null || _error_response2 === void 0 ? void 0 : (_error_response_data1 = _error_response2.data) === null || _error_response_data1 === void 0 ? void 0 : _error_response_data1.message) || error.message || \"Failed to activate DNS service\";\n            react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.error(\"Failed to activate DNS service: \" + errorMessage);\n        } finally{\n            setActivatingService(false);\n        }\n    };\n    // Add DNS Record\n    const addDnsRecord = async ()=>{\n        if (!formData.content.trim()) {\n            react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.error(\"Please enter the record content\");\n            return;\n        }\n        try {\n            setLoading(true);\n            const recordData = {\n                type: selectedRecordType,\n                name: formData.name,\n                content: formData.content,\n                ttl: parseInt(formData.ttl)\n            };\n            const response = await _app_services_domainMngService__WEBPACK_IMPORTED_MODULE_3__[\"default\"].addDnsRecord(domain.id, recordData);\n            if (response.data.success) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.success(\"\".concat(selectedRecordType, \" record added successfully!\"));\n                setFormData({\n                    name: \"@\",\n                    content: \"\",\n                    ttl: \"3600\"\n                });\n                setShowAddForm(false);\n                await loadDnsRecords(); // Reload records\n            } else {\n                throw new Error(response.data.error || \"Failed to add DNS record\");\n            }\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error(\"Error adding DNS record:\", error);\n            react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.error(\"Failed to add DNS record: \" + (((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.details) || error.message));\n        } finally{\n            setLoading(false);\n        }\n    };\n    // Copy to clipboard\n    const copyToClipboard = (text)=>{\n        navigator.clipboard.writeText(text);\n        react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.success(\"Copied to clipboard!\");\n    };\n    // Get record type icon and color\n    const getRecordTypeInfo = (type)=>{\n        const info = {\n            A: {\n                icon: _barrel_optimize_names_Activity_AlertCircle_CheckCircle_Copy_Edit_Globe_Plus_Power_Server_Shield_Trash2_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n                color: \"blue\",\n                bgColor: \"bg-blue-50\",\n                textColor: \"text-blue-700\"\n            },\n            AAAA: {\n                icon: _barrel_optimize_names_Activity_AlertCircle_CheckCircle_Copy_Edit_Globe_Plus_Power_Server_Shield_Trash2_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n                color: \"indigo\",\n                bgColor: \"bg-indigo-50\",\n                textColor: \"text-indigo-700\"\n            },\n            CNAME: {\n                icon: _barrel_optimize_names_Activity_AlertCircle_CheckCircle_Copy_Edit_Globe_Plus_Power_Server_Shield_Trash2_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n                color: \"purple\",\n                bgColor: \"bg-purple-50\",\n                textColor: \"text-purple-700\"\n            },\n            MX: {\n                icon: _barrel_optimize_names_Activity_AlertCircle_CheckCircle_Copy_Edit_Globe_Plus_Power_Server_Shield_Trash2_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n                color: \"green\",\n                bgColor: \"bg-green-50\",\n                textColor: \"text-green-700\"\n            },\n            TXT: {\n                icon: _barrel_optimize_names_Activity_AlertCircle_CheckCircle_Copy_Edit_Globe_Plus_Power_Server_Shield_Trash2_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n                color: \"orange\",\n                bgColor: \"bg-orange-50\",\n                textColor: \"text-orange-700\"\n            },\n            NS: {\n                icon: _barrel_optimize_names_Activity_AlertCircle_CheckCircle_Copy_Edit_Globe_Plus_Power_Server_Shield_Trash2_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n                color: \"gray\",\n                bgColor: \"bg-gray-50\",\n                textColor: \"text-gray-700\"\n            },\n            SRV: {\n                icon: _barrel_optimize_names_Activity_AlertCircle_CheckCircle_Copy_Edit_Globe_Plus_Power_Server_Shield_Trash2_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n                color: \"pink\",\n                bgColor: \"bg-pink-50\",\n                textColor: \"text-pink-700\"\n            }\n        };\n        return info[type] || info.A;\n    };\n    // Format record name for display\n    const formatRecordName = (name)=>{\n        if (name === \"@\" || name === \"\") {\n            return \"@ (\".concat(domain === null || domain === void 0 ? void 0 : domain.name, \")\");\n        }\n        return \"\".concat(name, \".\").concat(domain === null || domain === void 0 ? void 0 : domain.name);\n    };\n    // Available record types (only show implemented ones)\n    const availableRecordTypes = [\n        {\n            value: \"A\",\n            label: \"A Record\",\n            description: \"IPv4 Address\",\n            implemented: true\n        },\n        {\n            value: \"AAAA\",\n            label: \"AAAA Record\",\n            description: \"IPv6 Address\",\n            implemented: true\n        },\n        {\n            value: \"CNAME\",\n            label: \"CNAME Record\",\n            description: \"Domain Alias\",\n            implemented: false\n        },\n        {\n            value: \"MX\",\n            label: \"MX Record\",\n            description: \"Mail Server\",\n            implemented: false\n        },\n        {\n            value: \"TXT\",\n            label: \"TXT Record\",\n            description: \"Text Data\",\n            implemented: false\n        },\n        {\n            value: \"NS\",\n            label: \"NS Record\",\n            description: \"Name Server\",\n            implemented: false\n        },\n        {\n            value: \"SRV\",\n            label: \"SRV Record\",\n            description: \"Service Location\",\n            implemented: false\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                className: \"border-l-4 border-l-blue-500\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.CardBody, {\n                    className: \"p-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-3 rounded-full \".concat(dnsServiceActive ? \"bg-green-100\" : \"bg-gray-100\"),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_CheckCircle_Copy_Edit_Globe_Plus_Power_Server_Shield_Trash2_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            className: \"h-6 w-6 \".concat(dnsServiceActive ? \"text-green-600\" : \"text-gray-400\")\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                            lineNumber: 309,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                        lineNumber: 304,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Typography, {\n                                                variant: \"h5\",\n                                                className: \"text-gray-800 mb-1\",\n                                                children: \"DNS Service Status\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                                lineNumber: 316,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Chip, {\n                                                        value: dnsServiceActive ? \"Active\" : \"Inactive\",\n                                                        color: dnsServiceActive ? \"green\" : \"gray\",\n                                                        className: \"text-xs\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                                        lineNumber: 320,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Typography, {\n                                                        className: \"text-sm text-gray-600\",\n                                                        children: dnsServiceActive ? \"DNS service is active and ready to manage records\" : \"DNS service needs to be activated before managing records\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                                        lineNumber: 325,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                                lineNumber: 319,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                        lineNumber: 315,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                lineNumber: 303,\n                                columnNumber: 13\n                            }, this),\n                            !dnsServiceActive && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                className: \"bg-blue-600 hover:bg-blue-700 flex items-center gap-2\",\n                                onClick: activateDnsService,\n                                disabled: activatingService,\n                                children: [\n                                    activatingService ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-white\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                        lineNumber: 341,\n                                        columnNumber: 19\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_CheckCircle_Copy_Edit_Globe_Plus_Power_Server_Shield_Trash2_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                        lineNumber: 343,\n                                        columnNumber: 19\n                                    }, this),\n                                    \"Activate DNS Service\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                lineNumber: 335,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                        lineNumber: 302,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                    lineNumber: 301,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                lineNumber: 300,\n                columnNumber: 7\n            }, this),\n            dnsServiceActive && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.CardBody, {\n                        className: \"p-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Typography, {\n                                                variant: \"h5\",\n                                                className: \"text-gray-800 mb-1\",\n                                                children: \"DNS Records\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                                lineNumber: 360,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Typography, {\n                                                className: \"text-sm text-gray-600\",\n                                                children: \"Manage your domain's DNS records\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                                lineNumber: 363,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                        lineNumber: 359,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        className: \"bg-blue-600 hover:bg-blue-700 flex items-center gap-2\",\n                                        onClick: ()=>setShowAddForm(!showAddForm),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_CheckCircle_Copy_Edit_Globe_Plus_Power_Server_Shield_Trash2_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                className: \"h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                                lineNumber: 371,\n                                                columnNumber: 19\n                                            }, this),\n                                            \"Add Record\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                        lineNumber: 367,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                lineNumber: 358,\n                                columnNumber: 15\n                            }, this),\n                            showAddForm && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gray-50 p-6 rounded-lg mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Typography, {\n                                        variant: \"h6\",\n                                        className: \"text-gray-800 mb-4\",\n                                        children: \"Add New DNS Record\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                        lineNumber: 379,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Select, {\n                                                label: \"Record Type\",\n                                                value: selectedRecordType,\n                                                onChange: (val)=>setSelectedRecordType(val),\n                                                children: availableRecordTypes.map((type)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Option, {\n                                                        value: type.value,\n                                                        disabled: !type.implemented,\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between w-full\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: type.label\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                                                    lineNumber: 396,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                !type.implemented && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Chip, {\n                                                                    value: \"Soon\",\n                                                                    size: \"sm\",\n                                                                    color: \"amber\",\n                                                                    className: \"text-xs\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                                                    lineNumber: 398,\n                                                                    columnNumber: 31\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                                            lineNumber: 395,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, type.value, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                                        lineNumber: 390,\n                                                        columnNumber: 25\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                                lineNumber: 384,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Input, {\n                                                label: \"Name\",\n                                                value: formData.name,\n                                                onChange: (e)=>setFormData((prev)=>({\n                                                            ...prev,\n                                                            name: e.target.value\n                                                        })),\n                                                placeholder: \"@, www, mail, etc.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                                lineNumber: 410,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Input, {\n                                                label: selectedRecordType === \"A\" ? \"IPv4 Address\" : \"IPv6 Address\",\n                                                value: formData.content,\n                                                onChange: (e)=>setFormData((prev)=>({\n                                                            ...prev,\n                                                            content: e.target.value\n                                                        })),\n                                                placeholder: selectedRecordType === \"A\" ? \"***********\" : \"2001:db8::1\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                                lineNumber: 422,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Select, {\n                                                label: \"TTL\",\n                                                value: formData.ttl,\n                                                onChange: (val)=>setFormData((prev)=>({\n                                                            ...prev,\n                                                            ttl: val\n                                                        })),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Option, {\n                                                        value: \"300\",\n                                                        children: \"5 minutes\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                                        lineNumber: 449,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Option, {\n                                                        value: \"1800\",\n                                                        children: \"30 minutes\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                                        lineNumber: 450,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Option, {\n                                                        value: \"3600\",\n                                                        children: \"1 hour\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                                        lineNumber: 451,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Option, {\n                                                        value: \"14400\",\n                                                        children: \"4 hours\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                                        lineNumber: 452,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Option, {\n                                                        value: \"86400\",\n                                                        children: \"1 day\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                                        lineNumber: 453,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                                lineNumber: 442,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                        lineNumber: 383,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex gap-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                className: \"bg-green-600 hover:bg-green-700 flex items-center gap-2\",\n                                                onClick: addDnsRecord,\n                                                disabled: loading,\n                                                children: [\n                                                    loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-white\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                                        lineNumber: 464,\n                                                        columnNumber: 25\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_CheckCircle_Copy_Edit_Globe_Plus_Power_Server_Shield_Trash2_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                        className: \"h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                                        lineNumber: 466,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    \"Add Record\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                                lineNumber: 458,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                variant: \"outlined\",\n                                                onClick: ()=>setShowAddForm(false),\n                                                children: \"Cancel\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                                lineNumber: 470,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                        lineNumber: 457,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                lineNumber: 378,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-3\",\n                                children: loading && dnsRecords.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center py-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                            lineNumber: 484,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Typography, {\n                                            className: \"text-gray-600\",\n                                            children: \"Loading DNS records...\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                            lineNumber: 485,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                    lineNumber: 483,\n                                    columnNumber: 19\n                                }, this) : dnsRecords.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center py-12 bg-gray-50 rounded-lg\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_CheckCircle_Copy_Edit_Globe_Plus_Power_Server_Shield_Trash2_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                            className: \"h-12 w-12 text-gray-400 mx-auto mb-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                            lineNumber: 491,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Typography, {\n                                            variant: \"h6\",\n                                            className: \"text-gray-600 mb-2\",\n                                            children: \"No DNS Records Found\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                            lineNumber: 492,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Typography, {\n                                            className: \"text-sm text-gray-500 mb-4\",\n                                            children: \"Start by adding your first DNS record to configure your domain.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                            lineNumber: 495,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                            className: \"bg-blue-600 hover:bg-blue-700\",\n                                            onClick: ()=>setShowAddForm(true),\n                                            children: \"Add Your First Record\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                            lineNumber: 499,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                    lineNumber: 490,\n                                    columnNumber: 19\n                                }, this) : dnsRecords.map((record)=>{\n                                    const typeInfo = getRecordTypeInfo(record.type);\n                                    const IconComponent = typeInfo.icon;\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                        className: \"border border-gray-200 hover:shadow-md transition-shadow\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.CardBody, {\n                                            className: \"p-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-4 flex-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"p-2 rounded-lg \".concat(typeInfo.bgColor),\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(IconComponent, {\n                                                                    className: \"h-5 w-5 \".concat(typeInfo.textColor)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                                                    lineNumber: 522,\n                                                                    columnNumber: 33\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                                                lineNumber: 519,\n                                                                columnNumber: 31\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center gap-3 mb-1\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Chip, {\n                                                                                value: record.type,\n                                                                                color: typeInfo.color,\n                                                                                className: \"text-xs\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                                                                lineNumber: 529,\n                                                                                columnNumber: 35\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Typography, {\n                                                                                className: \"font-medium text-gray-900\",\n                                                                                children: formatRecordName(record.name)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                                                                lineNumber: 534,\n                                                                                columnNumber: 35\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Typography, {\n                                                                                className: \"text-xs text-gray-500\",\n                                                                                children: [\n                                                                                    \"TTL: \",\n                                                                                    record.ttl,\n                                                                                    \"s\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                                                                lineNumber: 537,\n                                                                                columnNumber: 35\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                                                        lineNumber: 528,\n                                                                        columnNumber: 33\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center gap-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Typography, {\n                                                                                className: \"font-mono text-sm text-gray-700\",\n                                                                                children: record.content\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                                                                lineNumber: 543,\n                                                                                columnNumber: 35\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Tooltip, {\n                                                                                content: \"Copy to clipboard\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.IconButton, {\n                                                                                    variant: \"text\",\n                                                                                    size: \"sm\",\n                                                                                    onClick: ()=>copyToClipboard(record.content),\n                                                                                    className: \"p-1\",\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_CheckCircle_Copy_Edit_Globe_Plus_Power_Server_Shield_Trash2_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                                        className: \"h-3 w-3\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                                                                        lineNumber: 555,\n                                                                                        columnNumber: 39\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                                                                    lineNumber: 547,\n                                                                                    columnNumber: 37\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                                                                lineNumber: 546,\n                                                                                columnNumber: 35\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                                                        lineNumber: 542,\n                                                                        columnNumber: 33\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                                                lineNumber: 527,\n                                                                columnNumber: 31\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                                        lineNumber: 518,\n                                                        columnNumber: 29\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Tooltip, {\n                                                                content: \"Edit record\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.IconButton, {\n                                                                    variant: \"text\",\n                                                                    size: \"sm\",\n                                                                    className: \"text-blue-600 hover:bg-blue-50\",\n                                                                    disabled: true,\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_CheckCircle_Copy_Edit_Globe_Plus_Power_Server_Shield_Trash2_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                        className: \"h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                                                        lineNumber: 570,\n                                                                        columnNumber: 35\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                                                    lineNumber: 564,\n                                                                    columnNumber: 33\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                                                lineNumber: 563,\n                                                                columnNumber: 31\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Tooltip, {\n                                                                content: \"Delete record\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.IconButton, {\n                                                                    variant: \"text\",\n                                                                    size: \"sm\",\n                                                                    className: \"text-red-600 hover:bg-red-50\",\n                                                                    disabled: true,\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_CheckCircle_Copy_Edit_Globe_Plus_Power_Server_Shield_Trash2_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                        className: \"h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                                                        lineNumber: 580,\n                                                                        columnNumber: 35\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                                                    lineNumber: 574,\n                                                                    columnNumber: 33\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                                                lineNumber: 573,\n                                                                columnNumber: 31\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                                        lineNumber: 562,\n                                                        columnNumber: 29\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                                lineNumber: 517,\n                                                columnNumber: 27\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                            lineNumber: 516,\n                                            columnNumber: 25\n                                        }, this)\n                                    }, record.id, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                        lineNumber: 512,\n                                        columnNumber: 23\n                                    }, this);\n                                })\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                lineNumber: 481,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                        lineNumber: 357,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                    lineNumber: 356,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Alert, {\n                color: \"blue\",\n                className: \"py-3\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_CheckCircle_Copy_Edit_Globe_Plus_Power_Server_Shield_Trash2_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                        className: \"h-4 w-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                        lineNumber: 598,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Typography, {\n                                className: \"font-medium mb-1\",\n                                children: \"DNS Management Information\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                lineNumber: 600,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                className: \"text-sm space-y-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: \"• Currently supporting A and AAAA record types\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                        lineNumber: 604,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: \"• Additional record types (CNAME, MX, TXT, NS, SRV) coming soon\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                        lineNumber: 605,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: \"• DNS changes may take 24-48 hours to propagate globally\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                        lineNumber: 608,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: \"• Always backup your DNS configuration before making changes\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                        lineNumber: 609,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                                lineNumber: 603,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                        lineNumber: 599,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n                lineNumber: 597,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ModernDnsManager.jsx\",\n        lineNumber: 298,\n        columnNumber: 5\n    }, this);\n}\n_s(ModernDnsManager, \"nJyRsWngjamWcnfdokhFJYjie4c=\");\n_c = ModernDnsManager;\nvar _c;\n$RefreshReg$(_c, \"ModernDnsManager\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/domains/ModernDnsManager.jsx\n"));

/***/ })

});