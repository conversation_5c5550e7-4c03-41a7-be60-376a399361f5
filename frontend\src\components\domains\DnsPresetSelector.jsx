"use client";
import { useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>eader,
  DialogBody,
  DialogFooter,
  Typography,
  Card,
  CardBody,
  Chip,
  Alert,
} from "@material-tailwind/react";
import { Zap, Info, Plus, AlertTriangle } from "lucide-react";
import { DNS_PRESETS } from "@/constants/dnsRecords";

export default function DnsPresetSelector({ onApplyPreset }) {
  const [isOpen, setIsOpen] = useState(false);
  const [selectedPreset, setSelectedPreset] = useState(null);
  const [isApplying, setIsApplying] = useState(false);

  const handleApplyPreset = async () => {
    if (!selectedPreset) return;

    try {
      setIsApplying(true);
      await onApplyPreset(DNS_PRESETS[selectedPreset].records);
      setIsOpen(false);
      setSelectedPreset(null);
    } catch (error) {
      console.error("Error applying preset:", error);
    } finally {
      setIsApplying(false);
    }
  };

  const getRecordTypeColor = (type) => {
    const colors = {
      A: "blue",
      AAAA: "blue",
      CNAME: "purple",
      MX: "green",
      TXT: "orange",
      NS: "gray",
      SRV: "pink",
    };
    return colors[type] || "gray";
  };

  return (
    <>
      <Button
        variant="outlined"
        className="border-blue-600 text-blue-600 hover:bg-blue-50 flex items-center gap-2"
        onClick={() => setIsOpen(true)}
      >
        <Zap className="h-4 w-4" />
        Quick Setup
      </Button>

      <Dialog open={isOpen} handler={() => setIsOpen(false)} size="lg">
        <DialogHeader className="flex items-center gap-2">
          <Zap className="h-5 w-5 text-blue-600" />
          DNS Quick Setup Presets
        </DialogHeader>

        <DialogBody className="space-y-4 max-h-96 overflow-y-auto">
          <Alert color="blue" className="py-2">
            <Info className="h-4 w-4" />
            <Typography className="text-sm">
              Choose a preset configuration to quickly set up common DNS records for your domain.
              You can modify these records after applying the preset.
            </Typography>
          </Alert>

          <div className="space-y-4">
            {Object.entries(DNS_PRESETS).map(([presetName, preset]) => (
              <Card
                key={presetName}
                className={`cursor-pointer border-2 transition-colors ${
                  selectedPreset === presetName
                    ? "border-blue-500 bg-blue-50"
                    : "border-gray-200 hover:border-gray-300"
                }`}
                onClick={() => setSelectedPreset(presetName)}
              >
                <CardBody className="p-4">
                  <div className="flex items-start justify-between mb-3">
                    <div>
                      <Typography variant="h6" className="text-gray-900">
                        {presetName}
                      </Typography>
                      <Typography className="text-sm text-gray-600">
                        {preset.description}
                      </Typography>
                    </div>
                    <div className="flex items-center gap-1">
                      <Typography className="text-xs text-gray-500">
                        {preset.records.length} records
                      </Typography>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Typography className="text-sm font-medium text-gray-700">
                      Records to be created:
                    </Typography>
                    <div className="space-y-1">
                      {preset.records.map((record, index) => (
                        <div
                          key={index}
                          className="flex items-center gap-2 text-sm bg-white p-2 rounded border"
                        >
                          <Chip
                            value={record.type}
                            color={getRecordTypeColor(record.type)}
                            size="sm"
                            className="text-xs"
                          />
                          <span className="font-medium">{record.name}</span>
                          <span className="text-gray-500">→</span>
                          <span className="font-mono text-xs flex-1 truncate">
                            {record.content}
                          </span>
                          {record.priority && (
                            <span className="text-xs text-gray-500">
                              Priority: {record.priority}
                            </span>
                          )}
                        </div>
                      ))}
                    </div>
                  </div>
                </CardBody>
              </Card>
            ))}
          </div>

          {selectedPreset && (
            <Alert color="amber" className="py-2">
              <AlertTriangle className="h-4 w-4" />
              <div>
                <Typography className="text-sm font-medium">Important Notes:</Typography>
                <ul className="text-xs mt-1 space-y-1">
                  <li>• This will create multiple DNS records at once</li>
                  <li>• Some records may contain placeholders that need to be updated</li>
                  <li>• You can edit or delete individual records after applying</li>
                  <li>• DNS changes may take 24-48 hours to propagate</li>
                </ul>
              </div>
            </Alert>
          )}
        </DialogBody>

        <DialogFooter className="flex gap-2">
          <Button
            variant="outlined"
            onClick={() => {
              setIsOpen(false);
              setSelectedPreset(null);
            }}
            disabled={isApplying}
          >
            Cancel
          </Button>
          <Button
            className="bg-blue-600 hover:bg-blue-700 flex items-center gap-2"
            onClick={handleApplyPreset}
            disabled={!selectedPreset || isApplying}
          >
            {isApplying ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                Applying...
              </>
            ) : (
              <>
                <Plus className="h-4 w-4" />
                Apply Preset
              </>
            )}
          </Button>
        </DialogFooter>
      </Dialog>
    </>
  );
}
