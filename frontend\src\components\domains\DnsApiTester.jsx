"use client";
import { useState } from "react";
import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  CardBody,
  Button,
  Input,
  Select,
  Option,
  Alert,
  Textarea,
} from "@material-tailwind/react";
import {
  Play,
  CheckCircle,
  XCircle,
  AlertCircle,
  Copy,
  Zap,
} from "lucide-react";
import domainMngService from "@/app/services/domainMngService";
import { toast } from "react-toastify";

export default function DnsApiTester({ domain }) {
  const [activeTest, setActiveTest] = useState("activate");
  const [loading, setLoading] = useState(false);
  const [results, setResults] = useState({});
  
  // Test data states
  const [orderId, setOrderId] = useState("");
  const [recordType, setRecordType] = useState("A");
  const [recordName, setRecordName] = useState("@");
  const [recordContent, setRecordContent] = useState("192.168.1.1");
  const [recordTtl, setRecordTtl] = useState("3600");
  const [recordPriority, setRecordPriority] = useState("10");

  // Test 1: Activate DNS Service
  const testActivateDns = async () => {
    if (!orderId.trim()) {
      toast.error("Please enter an Order ID");
      return;
    }

    setLoading(true);
    try {
      const response = await domainMngService.activateDnsService(orderId);
      setResults(prev => ({
        ...prev,
        activate: {
          success: true,
          data: response.data,
          timestamp: new Date().toISOString(),
        }
      }));
      toast.success("DNS service activation test completed");
    } catch (error) {
      setResults(prev => ({
        ...prev,
        activate: {
          success: false,
          error: error.response?.data || error.message,
          timestamp: new Date().toISOString(),
        }
      }));
      toast.error("DNS service activation test failed");
    } finally {
      setLoading(false);
    }
  };

  // Test 2: Get DNS Records
  const testGetDnsRecords = async () => {
    if (!domain?.id) {
      toast.error("Domain ID not available");
      return;
    }

    setLoading(true);
    try {
      const response = await domainMngService.getDnsRecords(domain.id);
      setResults(prev => ({
        ...prev,
        getDnsRecords: {
          success: true,
          data: response.data,
          timestamp: new Date().toISOString(),
        }
      }));
      toast.success("Get DNS records test completed");
    } catch (error) {
      setResults(prev => ({
        ...prev,
        getDnsRecords: {
          success: false,
          error: error.response?.data || error.message,
          timestamp: new Date().toISOString(),
        }
      }));
      toast.error("Get DNS records test failed");
    } finally {
      setLoading(false);
    }
  };

  // Test 3: Add DNS Record
  const testAddDnsRecord = async () => {
    if (!domain?.id) {
      toast.error("Domain ID not available");
      return;
    }

    if (!recordContent.trim()) {
      toast.error("Please enter record content");
      return;
    }

    setLoading(true);
    try {
      const recordData = {
        type: recordType,
        name: recordName,
        content: recordContent,
        ttl: parseInt(recordTtl),
      };

      // Add priority for MX and SRV records
      if (recordType === "MX" || recordType === "SRV") {
        recordData.priority = parseInt(recordPriority);
      }

      const response = await domainMngService.addDnsRecord(domain.id, recordData);
      setResults(prev => ({
        ...prev,
        addDnsRecord: {
          success: true,
          data: response.data,
          requestData: recordData,
          timestamp: new Date().toISOString(),
        }
      }));
      toast.success("Add DNS record test completed");
    } catch (error) {
      setResults(prev => ({
        ...prev,
        addDnsRecord: {
          success: false,
          error: error.response?.data || error.message,
          timestamp: new Date().toISOString(),
        }
      }));
      toast.error("Add DNS record test failed");
    } finally {
      setLoading(false);
    }
  };

  // Copy result to clipboard
  const copyResult = (result) => {
    navigator.clipboard.writeText(JSON.stringify(result, null, 2));
    toast.success("Result copied to clipboard");
  };

  // Render test result
  const renderResult = (testKey, title) => {
    const result = results[testKey];
    if (!result) return null;

    return (
      <Card className="mt-4">
        <CardBody className="p-4">
          <div className="flex items-center justify-between mb-3">
            <div className="flex items-center gap-2">
              {result.success ? (
                <CheckCircle className="h-5 w-5 text-green-600" />
              ) : (
                <XCircle className="h-5 w-5 text-red-600" />
              )}
              <Typography variant="h6" className="text-gray-800">
                {title} Result
              </Typography>
            </div>
            <div className="flex items-center gap-2">
              <Typography className="text-xs text-gray-500">
                {new Date(result.timestamp).toLocaleString()}
              </Typography>
              <Button
                size="sm"
                variant="text"
                onClick={() => copyResult(result)}
                className="p-1"
              >
                <Copy className="h-4 w-4" />
              </Button>
            </div>
          </div>

          <Alert color={result.success ? "green" : "red"} className="mb-3">
            <AlertCircle className="h-4 w-4" />
            <Typography className="font-medium">
              {result.success ? "Success" : "Error"}
            </Typography>
          </Alert>

          <div className="bg-gray-50 p-3 rounded-lg">
            <Typography className="text-xs font-mono whitespace-pre-wrap">
              {JSON.stringify(result.success ? result.data : result.error, null, 2)}
            </Typography>
          </div>

          {result.requestData && (
            <div className="mt-3">
              <Typography className="text-sm font-medium text-gray-700 mb-2">
                Request Data:
              </Typography>
              <div className="bg-blue-50 p-3 rounded-lg">
                <Typography className="text-xs font-mono whitespace-pre-wrap">
                  {JSON.stringify(result.requestData, null, 2)}
                </Typography>
              </div>
            </div>
          )}
        </CardBody>
      </Card>
    );
  };

  const tests = [
    {
      id: "activate",
      title: "1. Activate DNS Service",
      description: "Test DNS service activation for a domain order",
      action: testActivateDns,
    },
    {
      id: "getDnsRecords",
      title: "2. Get DNS Records",
      description: "Test retrieving DNS records for the domain",
      action: testGetDnsRecords,
    },
    {
      id: "addDnsRecord",
      title: "3. Add DNS Record",
      description: "Test adding a new DNS record",
      action: testAddDnsRecord,
    },
  ];

  return (
    <div className="space-y-6">
      {/* Domain Info */}
      <Alert color="blue" className="py-3">
        <AlertCircle className="h-4 w-4" />
        <div>
          <Typography className="font-medium">Testing Domain: {domain?.name}</Typography>
          <Typography className="text-sm">
            Domain ID: {domain?.id} | Order ID: {domain?.orderid || "Not available"}
          </Typography>
        </div>
      </Alert>

      {/* Test Selection */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        {tests.map((test) => (
          <Card
            key={test.id}
            className={`cursor-pointer border-2 transition-colors ${
              activeTest === test.id
                ? "border-blue-500 bg-blue-50"
                : "border-gray-200 hover:border-gray-300"
            }`}
            onClick={() => setActiveTest(test.id)}
          >
            <CardBody className="p-4">
              <Typography variant="h6" className="text-gray-800 mb-2">
                {test.title}
              </Typography>
              <Typography className="text-sm text-gray-600">
                {test.description}
              </Typography>
            </CardBody>
          </Card>
        ))}
      </div>

      {/* Test Forms */}
      <Card>
        <CardBody className="p-6">
          {activeTest === "activate" && (
            <div className="space-y-4">
              <Typography variant="h5" className="text-gray-800">
                Test DNS Service Activation
              </Typography>
              <Typography className="text-sm text-gray-600">
                Enter the domain order ID to activate DNS service. You can find this in your domain details.
              </Typography>
              
              <Input
                label="Domain Order ID"
                value={orderId}
                onChange={(e) => setOrderId(e.target.value)}
                placeholder="e.g., 12345678"
              />
              
              <Button
                className="bg-blue-600 hover:bg-blue-700 flex items-center gap-2"
                onClick={testActivateDns}
                disabled={loading}
              >
                {loading ? (
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                ) : (
                  <Play className="h-4 w-4" />
                )}
                Test Activation
              </Button>
            </div>
          )}

          {activeTest === "getDnsRecords" && (
            <div className="space-y-4">
              <Typography variant="h5" className="text-gray-800">
                Test Get DNS Records
              </Typography>
              <Typography className="text-sm text-gray-600">
                Retrieve all DNS records for the current domain.
              </Typography>
              
              <Button
                className="bg-blue-600 hover:bg-blue-700 flex items-center gap-2"
                onClick={testGetDnsRecords}
                disabled={loading}
              >
                {loading ? (
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                ) : (
                  <Play className="h-4 w-4" />
                )}
                Test Get Records
              </Button>
            </div>
          )}

          {activeTest === "addDnsRecord" && (
            <div className="space-y-4">
              <Typography variant="h5" className="text-gray-800">
                Test Add DNS Record
              </Typography>
              <Typography className="text-sm text-gray-600">
                Add a new DNS record to test the API integration.
              </Typography>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Select
                  label="Record Type"
                  value={recordType}
                  onChange={(val) => setRecordType(val)}
                >
                  <Option value="A">A Record (IPv4)</Option>
                  <Option value="AAAA">AAAA Record (IPv6)</Option>
                  <Option value="CNAME">CNAME Record</Option>
                  <Option value="MX">MX Record</Option>
                  <Option value="TXT">TXT Record</Option>
                  <Option value="NS">NS Record</Option>
                  <Option value="SRV">SRV Record</Option>
                </Select>
                
                <Input
                  label="Record Name"
                  value={recordName}
                  onChange={(e) => setRecordName(e.target.value)}
                  placeholder="@, www, mail, etc."
                />
              </div>
              
              <Input
                label="Record Content"
                value={recordContent}
                onChange={(e) => setRecordContent(e.target.value)}
                placeholder="IP address, domain name, or text content"
              />
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Input
                  label="TTL (seconds)"
                  value={recordTtl}
                  onChange={(e) => setRecordTtl(e.target.value)}
                  placeholder="3600"
                />
                
                {(recordType === "MX" || recordType === "SRV") && (
                  <Input
                    label="Priority"
                    value={recordPriority}
                    onChange={(e) => setRecordPriority(e.target.value)}
                    placeholder="10"
                  />
                )}
              </div>
              
              <Button
                className="bg-blue-600 hover:bg-blue-700 flex items-center gap-2"
                onClick={testAddDnsRecord}
                disabled={loading}
              >
                {loading ? (
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                ) : (
                  <Play className="h-4 w-4" />
                )}
                Test Add Record
              </Button>
            </div>
          )}
        </CardBody>
      </Card>

      {/* Results */}
      {renderResult("activate", "DNS Service Activation")}
      {renderResult("getDnsRecords", "Get DNS Records")}
      {renderResult("addDnsRecord", "Add DNS Record")}

      {/* API Information */}
      <Alert color="amber" className="py-3">
        <AlertCircle className="h-4 w-4" />
        <div>
          <Typography className="font-medium mb-2">Important Notes:</Typography>
          <ul className="text-sm space-y-1">
            <li>• These tests call the actual Heberjahiz API endpoints</li>
            <li>• Make sure you have valid domain order IDs for testing</li>
            <li>• DNS service must be activated before managing records</li>
            <li>• Check the browser console for detailed API responses</li>
            <li>• The domain name lookup function needs to be implemented in the backend</li>
          </ul>
        </div>
      </Alert>
    </div>
  );
}
