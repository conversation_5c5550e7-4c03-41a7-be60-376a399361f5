"use client";
import { useState, useEffect } from "react";
import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  CardBody,
  Button,
  Input,
  Select,
  Option,
  Alert,
  Chip,
  IconButton,
  Tooltip,
} from "@material-tailwind/react";
import {
  Plus,
  Globe,
  Server,
  Zap,
  CheckCircle,
  AlertCircle,
  Copy,
  Edit,
  Trash2,
  Power,
  Wifi,
  Shield,
  Activity,
} from "lucide-react";
import domainMngService from "@/app/services/domainMngService";
import { toast, ToastContainer } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";

export default function ModernDnsManager({ domain, onUpdate }) {
  const [dnsServiceActive, setDnsServiceActive] = useState(false);
  const [dnsRecords, setDnsRecords] = useState([]);
  const [loading, setLoading] = useState(false);
  const [activatingService, setActivatingService] = useState(false);
  const [showAddForm, setShowAddForm] = useState(false);
  const [selectedRecordType, setSelectedRecordType] = useState("A");

  // Form states for adding records
  const [formData, setFormData] = useState({
    name: "@",
    content: "",
    ttl: "3600",
  });

  // Load DNS records
  const loadDnsRecords = async () => {
    try {
      setLoading(true);
      const response = await domainMngService.getDnsRecords(domain.id, {
        type: selectedRecordType,
      });
      if (response.data.success) {
        setDnsRecords(response.data.records || []);
        setDnsServiceActive(true); // If we can get records, service is active
      }
    } catch (error) {
      console.error("Error loading DNS records:", error);
      // If we can't get records, service might not be activated
      setDnsServiceActive(false);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (domain?.id) {
      loadDnsRecords();
    }
  }, [domain?.id]);

  // Activate DNS Service
  const activateDnsService = async () => {
    console.log("Activating DNS service for domain:", domain);
    if (!domain?.orderId) {
      toast.error("Domain order ID not found. Cannot activate DNS service.");
      return;
    }

    try {
      setActivatingService(true);
      const response = await domainMngService.activateDnsService(
        domain.domainOrderId || domain.orderid || domain.orderId
      ); // Use the correct order ID field

      if (response.data.success) {
        setDnsServiceActive(true);
        toast.success("DNS service activated successfully!");
        await loadDnsRecords(); // Reload records after activation
      } else {
        throw new Error(
          response.data.error || "Failed to activate DNS service"
        );
      }
    } catch (error) {
      console.error("Error activating DNS service:", error);
      toast.error(
        "Failed to activate DNS service: " +
          (error.response?.data?.details || error.message)
      );
    } finally {
      setActivatingService(false);
    }
  };

  // Add DNS Record
  const addDnsRecord = async () => {
    if (!formData.content.trim()) {
      toast.error("Please enter the record content");
      return;
    }

    try {
      setLoading(true);
      const recordData = {
        type: selectedRecordType,
        name: formData.name,
        content: formData.content,
        ttl: parseInt(formData.ttl),
      };

      const response = await domainMngService.addDnsRecord(
        domain.id,
        recordData
      );

      if (response.data.success) {
        toast.success(`${selectedRecordType} record added successfully!`);
        setFormData({ name: "@", content: "", ttl: "3600" });
        setShowAddForm(false);
        await loadDnsRecords(); // Reload records
      } else {
        throw new Error(response.data.error || "Failed to add DNS record");
      }
    } catch (error) {
      console.error("Error adding DNS record:", error);
      toast.error(
        "Failed to add DNS record: " +
          (error.response?.data?.details || error.message)
      );
    } finally {
      setLoading(false);
    }
  };

  // Copy to clipboard
  const copyToClipboard = (text) => {
    navigator.clipboard.writeText(text);
    toast.success("Copied to clipboard!");
  };

  // Get record type icon and color
  const getRecordTypeInfo = (type) => {
    const info = {
      A: {
        icon: Globe,
        color: "blue",
        bgColor: "bg-blue-50",
        textColor: "text-blue-700",
      },
      AAAA: {
        icon: Globe,
        color: "indigo",
        bgColor: "bg-indigo-50",
        textColor: "text-indigo-700",
      },
      CNAME: {
        icon: Server,
        color: "purple",
        bgColor: "bg-purple-50",
        textColor: "text-purple-700",
      },
      MX: {
        icon: Wifi,
        color: "green",
        bgColor: "bg-green-50",
        textColor: "text-green-700",
      },
      TXT: {
        icon: Shield,
        color: "orange",
        bgColor: "bg-orange-50",
        textColor: "text-orange-700",
      },
      NS: {
        icon: Activity,
        color: "gray",
        bgColor: "bg-gray-50",
        textColor: "text-gray-700",
      },
      SRV: {
        icon: Server,
        color: "pink",
        bgColor: "bg-pink-50",
        textColor: "text-pink-700",
      },
    };
    return info[type] || info.A;
  };

  // Format record name for display
  const formatRecordName = (name) => {
    if (name === "@" || name === "") {
      return `@ (${domain?.name})`;
    }
    return `${name}.${domain?.name}`;
  };

  // Available record types (only show implemented ones)
  const availableRecordTypes = [
    {
      value: "A",
      label: "A Record",
      description: "IPv4 Address",
      implemented: true,
    },
    {
      value: "AAAA",
      label: "AAAA Record",
      description: "IPv6 Address",
      implemented: true,
    },
    {
      value: "CNAME",
      label: "CNAME Record",
      description: "Domain Alias",
      implemented: false,
    },
    {
      value: "MX",
      label: "MX Record",
      description: "Mail Server",
      implemented: false,
    },
    {
      value: "TXT",
      label: "TXT Record",
      description: "Text Data",
      implemented: false,
    },
    {
      value: "NS",
      label: "NS Record",
      description: "Name Server",
      implemented: false,
    },
    {
      value: "SRV",
      label: "SRV Record",
      description: "Service Location",
      implemented: false,
    },
  ];

  return (
    <div className="space-y-6">
      {/* DNS Service Status */}
      <Card className="border-l-4 border-l-blue-500">
        <CardBody className="p-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <div
                className={`p-3 rounded-full ${
                  dnsServiceActive ? "bg-green-100" : "bg-gray-100"
                }`}
              >
                <Power
                  className={`h-6 w-6 ${
                    dnsServiceActive ? "text-green-600" : "text-gray-400"
                  }`}
                />
              </div>
              <div>
                <Typography variant="h5" className="text-gray-800 mb-1">
                  DNS Service Status
                </Typography>
                <div className="flex items-center gap-2">
                  <Chip
                    value={dnsServiceActive ? "Active" : "Inactive"}
                    color={dnsServiceActive ? "green" : "gray"}
                    className="text-xs"
                  />
                  <Typography className="text-sm text-gray-600">
                    {dnsServiceActive
                      ? "DNS service is active and ready to manage records"
                      : "DNS service needs to be activated before managing records"}
                  </Typography>
                </div>
              </div>
            </div>

            {!dnsServiceActive && (
              <Button
                className="bg-blue-600 hover:bg-blue-700 flex items-center gap-2"
                onClick={activateDnsService}
                disabled={activatingService}
              >
                {activatingService ? (
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                ) : (
                  <Zap className="h-4 w-4" />
                )}
                Activate DNS Service
              </Button>
            )}
          </div>
        </CardBody>
      </Card>

      {/* DNS Records Management */}
      {dnsServiceActive && (
        <>
          {/* Quick Add Record Section */}
          <Card>
            <CardBody className="p-6">
              <div className="flex items-center justify-between mb-6">
                <div>
                  <Typography variant="h5" className="text-gray-800 mb-1">
                    DNS Records
                  </Typography>
                  <Typography className="text-sm text-gray-600">
                    Manage your domain's DNS records
                  </Typography>
                </div>
                <Button
                  className="bg-blue-600 hover:bg-blue-700 flex items-center gap-2"
                  onClick={() => setShowAddForm(!showAddForm)}
                >
                  <Plus className="h-4 w-4" />
                  Add Record
                </Button>
              </div>

              {/* Add Record Form */}
              {showAddForm && (
                <div className="bg-gray-50 p-6 rounded-lg mb-6">
                  <Typography variant="h6" className="text-gray-800 mb-4">
                    Add New DNS Record
                  </Typography>

                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-4">
                    <Select
                      label="Record Type"
                      value={selectedRecordType}
                      onChange={(val) => setSelectedRecordType(val)}
                    >
                      {availableRecordTypes.map((type) => (
                        <Option
                          key={type.value}
                          value={type.value}
                          disabled={!type.implemented}
                        >
                          <div className="flex items-center justify-between w-full">
                            <span>{type.label}</span>
                            {!type.implemented && (
                              <Chip
                                value="Soon"
                                size="sm"
                                color="amber"
                                className="text-xs"
                              />
                            )}
                          </div>
                        </Option>
                      ))}
                    </Select>

                    <Input
                      label="Name"
                      value={formData.name}
                      onChange={(e) =>
                        setFormData((prev) => ({
                          ...prev,
                          name: e.target.value,
                        }))
                      }
                      placeholder="@, www, mail, etc."
                    />

                    <Input
                      label={
                        selectedRecordType === "A"
                          ? "IPv4 Address"
                          : "IPv6 Address"
                      }
                      value={formData.content}
                      onChange={(e) =>
                        setFormData((prev) => ({
                          ...prev,
                          content: e.target.value,
                        }))
                      }
                      placeholder={
                        selectedRecordType === "A"
                          ? "***********"
                          : "2001:db8::1"
                      }
                    />

                    <Select
                      label="TTL"
                      value={formData.ttl}
                      onChange={(val) =>
                        setFormData((prev) => ({ ...prev, ttl: val }))
                      }
                    >
                      <Option value="300">5 minutes</Option>
                      <Option value="1800">30 minutes</Option>
                      <Option value="3600">1 hour</Option>
                      <Option value="14400">4 hours</Option>
                      <Option value="86400">1 day</Option>
                    </Select>
                  </div>

                  <div className="flex gap-3">
                    <Button
                      className="bg-green-600 hover:bg-green-700 flex items-center gap-2"
                      onClick={addDnsRecord}
                      disabled={loading}
                    >
                      {loading ? (
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                      ) : (
                        <CheckCircle className="h-4 w-4" />
                      )}
                      Add Record
                    </Button>
                    <Button
                      variant="outlined"
                      onClick={() => setShowAddForm(false)}
                    >
                      Cancel
                    </Button>
                  </div>
                </div>
              )}

              {/* Records List */}
              <div className="space-y-3">
                {loading && dnsRecords.length === 0 ? (
                  <div className="text-center py-8">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
                    <Typography className="text-gray-600">
                      Loading DNS records...
                    </Typography>
                  </div>
                ) : dnsRecords.length === 0 ? (
                  <div className="text-center py-12 bg-gray-50 rounded-lg">
                    <Globe className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                    <Typography variant="h6" className="text-gray-600 mb-2">
                      No DNS Records Found
                    </Typography>
                    <Typography className="text-sm text-gray-500 mb-4">
                      Start by adding your first DNS record to configure your
                      domain.
                    </Typography>
                    <Button
                      className="bg-blue-600 hover:bg-blue-700"
                      onClick={() => setShowAddForm(true)}
                    >
                      Add Your First Record
                    </Button>
                  </div>
                ) : (
                  dnsRecords.map((record) => {
                    const typeInfo = getRecordTypeInfo(record.type);
                    const IconComponent = typeInfo.icon;

                    return (
                      <Card
                        key={record.id}
                        className="border border-gray-200 hover:shadow-md transition-shadow"
                      >
                        <CardBody className="p-4">
                          <div className="flex items-center justify-between">
                            <div className="flex items-center gap-4 flex-1">
                              <div
                                className={`p-2 rounded-lg ${typeInfo.bgColor}`}
                              >
                                <IconComponent
                                  className={`h-5 w-5 ${typeInfo.textColor}`}
                                />
                              </div>

                              <div className="flex-1">
                                <div className="flex items-center gap-3 mb-1">
                                  <Chip
                                    value={record.type}
                                    color={typeInfo.color}
                                    className="text-xs"
                                  />
                                  <Typography className="font-medium text-gray-900">
                                    {formatRecordName(record.name)}
                                  </Typography>
                                  <Typography className="text-xs text-gray-500">
                                    TTL: {record.ttl}s
                                  </Typography>
                                </div>

                                <div className="flex items-center gap-2">
                                  <Typography className="font-mono text-sm text-gray-700">
                                    {record.content}
                                  </Typography>
                                  <Tooltip content="Copy to clipboard">
                                    <IconButton
                                      variant="text"
                                      size="sm"
                                      onClick={() =>
                                        copyToClipboard(record.content)
                                      }
                                      className="p-1"
                                    >
                                      <Copy className="h-3 w-3" />
                                    </IconButton>
                                  </Tooltip>
                                </div>
                              </div>
                            </div>

                            <div className="flex items-center gap-2">
                              <Tooltip content="Edit record">
                                <IconButton
                                  variant="text"
                                  size="sm"
                                  className="text-blue-600 hover:bg-blue-50"
                                  disabled
                                >
                                  <Edit className="h-4 w-4" />
                                </IconButton>
                              </Tooltip>
                              <Tooltip content="Delete record">
                                <IconButton
                                  variant="text"
                                  size="sm"
                                  className="text-red-600 hover:bg-red-50"
                                  disabled
                                >
                                  <Trash2 className="h-4 w-4" />
                                </IconButton>
                              </Tooltip>
                            </div>
                          </div>
                        </CardBody>
                      </Card>
                    );
                  })
                )}
              </div>
            </CardBody>
          </Card>
        </>
      )}

      {/* Information Alert */}
      <Alert color="blue" className="py-3">
        <AlertCircle className="h-4 w-4" />
        <div>
          <Typography className="font-medium mb-1">
            DNS Management Information
          </Typography>
          <ul className="text-sm space-y-1">
            <li>• Currently supporting A and AAAA record types</li>
            <li>
              • Additional record types (CNAME, MX, TXT, NS, SRV) coming soon
            </li>
            <li>• DNS changes may take 24-48 hours to propagate globally</li>
            <li>
              • Always backup your DNS configuration before making changes
            </li>
          </ul>
        </div>
      </Alert>
    </div>
  );
}
